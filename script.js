// 仓库库存管理系统 JavaScript

// 数据结构定义
class InventoryItem {
    constructor(code, name, specification, unit, quantity, minStock, remark = '') {
        this.code = code;
        this.name = name;
        this.specification = specification || '';
        this.unit = unit;
        this.quantity = quantity;
        this.minStock = minStock;
        this.remark = remark;
        this.lastUpdated = new Date().toLocaleString('zh-CN');
    }

    getStatus() {
        if (this.quantity <= 0) return 'out';
        if (this.quantity <= this.minStock) return 'low';
        return 'normal';
    }

    getStatusText() {
        const status = this.getStatus();
        switch (status) {
            case 'out': return '缺货';
            case 'low': return '库存不足';
            case 'normal': return '正常';
            default: return '未知';
        }
    }
}

// 出入库记录类
class TransactionRecord {
    constructor(type, productCode, productName, quantity, unit, reason = '', remark = '', operator = '系统') {
        this.id = Date.now() + Math.random().toString(36).substring(2, 11);
        this.type = type; // 'inbound' 或 'outbound'
        this.productCode = productCode;
        this.productName = productName;
        this.quantity = quantity;
        this.unit = unit;
        this.reason = reason;
        this.remark = remark;
        this.operator = operator;
        this.timestamp = new Date().toLocaleString('zh-CN');
        this.date = new Date().toLocaleDateString('zh-CN');
    }

    getTypeText() {
        return this.type === 'inbound' ? '入库' : '出库';
    }
}

// 组合套餐类
class ComboPackage {
    constructor(id, name, description, items) {
        this.id = id || Date.now().toString();
        this.name = name;
        this.description = description || '';
        this.items = items || []; // [{code: 'P001', quantity: 2}, ...]
        this.createdAt = new Date().toLocaleString('zh-CN');
        this.updatedAt = new Date().toLocaleString('zh-CN');
    }

    // 获取套餐可用数量（基于最少库存的商品）
    getAvailableQuantity(inventory) {
        if (this.items.length === 0) return 0;

        let minAvailable = Infinity;
        for (const item of this.items) {
            const inventoryItem = inventory.find(inv => inv.code === item.code);
            if (!inventoryItem || inventoryItem.quantity <= 0) {
                return 0; // 任何一个商品缺货，套餐就不可用
            }
            const availableForThisItem = Math.floor(inventoryItem.quantity / item.quantity);
            minAvailable = Math.min(minAvailable, availableForThisItem);
        }

        return minAvailable === Infinity ? 0 : minAvailable;
    }

    // 检查套餐是否可用
    isAvailable(inventory) {
        return this.getAvailableQuantity(inventory) > 0;
    }

    // 获取套餐状态
    getStatus(inventory) {
        const available = this.getAvailableQuantity(inventory);
        if (available === 0) return 'unavailable';
        if (available <= 3) return 'low';
        return 'normal';
    }

    // 获取套餐中缺货的商品
    getUnavailableItems(inventory) {
        return this.items.filter(item => {
            const inventoryItem = inventory.find(inv => inv.code === item.code);
            return !inventoryItem || inventoryItem.quantity < item.quantity;
        });
    }
}

// 全局变量
let inventory = [];
let transactionHistory = [];
let currentTab = 'inventory';
let isEditMode = false;
let developerMode = false;
let titleClickCount = 0;
let titleClickTimer = null;
let outboundMode = 'single'; // 'single' 或 'batch'
let outboundCart = []; // 批量出库购物车
let comboPackages = []; // 组合套餐列表
let selectedItems = []; // 批量编辑选中的商品

// 初始化应用
document.addEventListener('DOMContentLoaded', function() {
    loadInventoryData();
    loadTransactionHistory();
    loadComboPackages();
    initializeEventListeners();
    switchTab('inventory');
    updateInventoryDisplay();
    updateOutboundInterface();
    updateProductNameSuggestions();
    updateSpecificationSuggestions();
    updateCategoryFilter();
});

// 加载库存数据
function loadInventoryData() {
    const savedData = localStorage.getItem('warehouseInventory');
    if (savedData) {
        const data = JSON.parse(savedData);
        inventory = data.map(item => Object.assign(new InventoryItem(), item));
    } else {
        // 初始化示例数据
        inventory = [
            new InventoryItem('P001', '苹果', '红富士', '箱', 50, 10, '新鲜水果'),
            new InventoryItem('P002', '香蕉', '进口', '箱', 30, 5, '热带水果'),
            new InventoryItem('P003', '橙子', '脐橙', '箱', 8, 15, '维C丰富'),
        ];
        saveInventoryData();
    }
}

// 保存库存数据
function saveInventoryData() {
    localStorage.setItem('warehouseInventory', JSON.stringify(inventory));
}

// 加载交易历史
function loadTransactionHistory() {
    const savedData = localStorage.getItem('warehouseTransactions');
    if (savedData) {
        const data = JSON.parse(savedData);
        transactionHistory = data.map(item => Object.assign(new TransactionRecord(), item));
    } else {
        transactionHistory = [];
    }
}

// 保存交易历史
function saveTransactionHistory() {
    localStorage.setItem('warehouseTransactions', JSON.stringify(transactionHistory));
}

// 添加交易记录
function addTransactionRecord(type, productCode, productName, quantity, unit, reason = '', remark = '') {
    const record = new TransactionRecord(type, productCode, productName, quantity, unit, reason, remark);
    transactionHistory.unshift(record); // 最新记录在前
    saveTransactionHistory();
    return record;
}

// 加载组合套餐数据
function loadComboPackages() {
    const savedData = localStorage.getItem('warehouseComboPackages');
    if (savedData) {
        const data = JSON.parse(savedData);
        comboPackages = data.map(item => Object.assign(new ComboPackage(), item));
    } else {
        // 初始化示例套餐
        comboPackages = [
            new ComboPackage('combo001', '水果礼盒A', '苹果+橙子组合', [
                {code: 'AP001', quantity: 2},
                {code: 'OR001', quantity: 1}
            ]),
            new ComboPackage('combo002', '水果礼盒B', '香蕉+葡萄组合', [
                {code: 'BN001', quantity: 1},
                {code: 'GP001', quantity: 1}
            ])
        ];
        saveComboPackages();
    }
}

// 保存组合套餐数据
function saveComboPackages() {
    localStorage.setItem('warehouseComboPackages', JSON.stringify(comboPackages));
}

// 生成商品编号
function generateProductCode(productName) {
    // 获取商品名称的拼音首字母或使用固定前缀
    const prefix = getProductPrefix(productName);

    // 查找相同名称的商品
    const sameNameProducts = inventory.filter(item => item.name === productName);

    if (sameNameProducts.length === 0) {
        // 如果是全新的商品名称，生成基础编号
        const baseNumber = getNextBaseNumber(prefix);
        return `${prefix}${baseNumber.toString().padStart(3, '0')}`;
    } else {
        // 如果已有相同名称的商品，生成子编号
        const baseCode = sameNameProducts[0].code.split('-')[0]; // 获取基础编号部分
        const maxSubNumber = Math.max(
            ...sameNameProducts
                .map(item => {
                    const parts = item.code.split('-');
                    return parts.length > 1 ? parseInt(parts[1]) : 1;
                })
                .filter(num => !isNaN(num))
        );
        const nextSubNumber = maxSubNumber + 1;
        return `${baseCode}-${nextSubNumber.toString().padStart(2, '0')}`;
    }
}

// 获取商品前缀
function getProductPrefix(productName) {
    // 简单的前缀生成逻辑，可以根据需要扩展
    const prefixMap = {
        '苹果': 'AP',
        '香蕉': 'BN',
        '橙子': 'OR',
        '梨': 'PR',
        '葡萄': 'GP',
        '西瓜': 'WM',
        '草莓': 'SB',
        '桃子': 'PC'
    };

    return prefixMap[productName] || 'P';
}

// 获取下一个基础编号
function getNextBaseNumber(prefix) {
    const existingCodes = inventory
        .map(item => item.code)
        .filter(code => code.startsWith(prefix))
        .map(code => {
            const match = code.match(new RegExp(`^${prefix}(\\d+)`));
            return match ? parseInt(match[1]) : 0;
        })
        .filter(num => !isNaN(num));

    if (existingCodes.length === 0) {
        return 1;
    }

    return Math.max(...existingCodes) + 1;
}

// 检查编号是否已存在
function isCodeExists(code) {
    return inventory.some(item => item.code === code);
}

// 智能编号建议
function suggestProductCode(productName, specification = '') {
    let suggestedCode = generateProductCode(productName, specification);

    // 确保编号唯一
    let counter = 1;
    while (isCodeExists(suggestedCode)) {
        const parts = suggestedCode.split('-');
        if (parts.length > 1) {
            const baseCode = parts[0];
            const subNumber = parseInt(parts[1]) + counter;
            suggestedCode = `${baseCode}-${subNumber.toString().padStart(2, '0')}`;
        } else {
            suggestedCode = `${suggestedCode}-${counter.toString().padStart(2, '0')}`;
        }
        counter++;
    }

    return suggestedCode;
}

// 更新商品名称建议列表
function updateProductNameSuggestions() {
    const datalist = document.getElementById('productNameSuggestions');
    const uniqueNames = [...new Set(inventory.map(item => item.name))];

    datalist.innerHTML = '';
    uniqueNames.forEach(name => {
        const option = document.createElement('option');
        option.value = name;
        datalist.appendChild(option);
    });
}

// 更新规格建议列表
function updateSpecificationSuggestions(productName = '') {
    const datalist = document.getElementById('specificationSuggestions');

    if (!datalist) return;

    let specifications = [];

    if (productName) {
        // 如果指定了商品名称，只显示该商品的规格
        specifications = inventory
            .filter(item => item.name === productName)
            .map(item => item.specification)
            .filter(spec => spec && spec.trim() !== '');
    } else {
        // 如果没有指定商品名称，显示所有规格
        specifications = inventory
            .map(item => item.specification)
            .filter(spec => spec && spec.trim() !== '');
    }

    // 去重并排序
    const uniqueSpecs = [...new Set(specifications)].sort();

    datalist.innerHTML = '';
    uniqueSpecs.forEach(spec => {
        const option = document.createElement('option');
        option.value = spec;
        datalist.appendChild(option);
    });
}

// 处理商品名称输入
function handleProductNameInput(event) {
    const productName = event.target.value.trim();
    if (productName) {
        // 实时更新建议的编号
        const suggestedCode = suggestProductCode(productName);
        document.getElementById('inProductCode').value = suggestedCode;
    } else {
        document.getElementById('inProductCode').value = '';
    }
}

// 处理商品名称变更
function handleProductNameChange(event) {
    const productName = event.target.value.trim();
    if (productName) {
        // 更新规格建议列表
        updateSpecificationSuggestions(productName);

        // 检查是否是现有商品名称
        const existingProducts = inventory.filter(item => item.name === productName);
        if (existingProducts.length > 0) {
            // 如果是现有商品，可以预填一些信息
            const firstProduct = existingProducts[0];
            const unitField = document.getElementById('inUnit');
            const minStockField = document.getElementById('inMinStock');

            if (!unitField.value) {
                unitField.value = firstProduct.unit;
            }
            if (!minStockField.value || minStockField.value === '10') {
                minStockField.value = firstProduct.minStock;
            }

            // 显示提示信息
            showMessage(`检测到现有商品"${productName}"，已自动填充相关信息`, 'success');
        }

        // 更新编号
        const suggestedCode = suggestProductCode(productName);
        document.getElementById('inProductCode').value = suggestedCode;
    } else {
        // 如果商品名称为空，显示所有规格
        updateSpecificationSuggestions();
    }
}

// 处理规格变更
function handleSpecificationChange(event) {
    const specification = event.target.value.trim();
    const productName = document.getElementById('inProductName').value.trim();

    if (productName && specification) {
        // 查找具有相同名称和规格的商品
        const matchingItem = inventory.find(item =>
            item.name === productName &&
            item.specification === specification
        );

        if (matchingItem) {
            // 如果找到匹配的商品，自动填充单位和最低库存
            const unitField = document.getElementById('inUnit');
            const minStockField = document.getElementById('inMinStock');

            if (!unitField.value) {
                unitField.value = matchingItem.unit;
            }
            if (!minStockField.value || minStockField.value === '10') {
                minStockField.value = matchingItem.minStock;
            }

            // 更新商品编号
            const suggestedCode = suggestProductCode(productName, specification);
            document.getElementById('inProductCode').value = suggestedCode;

            // 显示提示信息
            showMessage(`检测到现有商品规格，已自动填充相关信息`, 'success');
        }
    }
}

// 初始化事件监听器
function initializeEventListeners() {
    // 导航按钮事件
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const tab = this.getAttribute('data-tab');
            switchTab(tab);
        });
    });

    // 入库表单事件
    document.getElementById('inboundForm').addEventListener('submit', handleInbound);
    
    // 出库表单事件
    document.getElementById('outboundForm').addEventListener('submit', handleOutbound);

    // 搜索事件
    document.getElementById('searchInput').addEventListener('input', searchInventory);

    // 编辑表单事件
    document.getElementById('editForm').addEventListener('submit', handleEdit);

    // 交易搜索事件
    document.getElementById('transactionSearchInput').addEventListener('input', searchTransactions);
    document.getElementById('transactionTypeFilter').addEventListener('change', searchTransactions);

    // 确认对话框事件
    document.getElementById('confirmYes').addEventListener('click', executeConfirmedAction);
    document.getElementById('confirmNo').addEventListener('click', closeConfirmModal);

    // 商品名称输入事件
    document.getElementById('inProductName').addEventListener('input', handleProductNameInput);
    document.getElementById('inProductName').addEventListener('change', handleProductNameChange);

    // 规格输入事件
    document.getElementById('inSpecification').addEventListener('change', handleSpecificationChange);

    // 库存搜索和筛选事件
    document.getElementById('searchInput').addEventListener('input', searchInventory);
    document.getElementById('categoryFilter').addEventListener('change', searchInventory);
    document.getElementById('statusFilter').addEventListener('change', searchInventory);

    // 出库商品搜索事件
    document.getElementById('outProductSearch').addEventListener('input', handleProductSearch);
    document.getElementById('outProductSearch').addEventListener('focus', showSearchResults);
    document.getElementById('productCategoryFilter').addEventListener('change', updateQuickSelectCards);

    // 点击其他地方隐藏搜索结果
    document.addEventListener('click', function(e) {
        if (!e.target.closest('.form-group')) {
            hideSearchResults();
        }
    });

    // 出库模式切换事件
    document.querySelectorAll('.mode-btn').forEach(btn => {
        btn.addEventListener('click', function() {
            const mode = this.getAttribute('data-mode');
            switchOutboundMode(mode);
        });
    });

    // 批量出库搜索事件
    document.getElementById('batchProductSearch').addEventListener('input', handleBatchProductSearch);
    document.getElementById('batchCategoryFilter').addEventListener('change', updateBatchQuickSelectCards);
}

// 切换标签页
function switchTab(tabName) {
    // 更新导航按钮状态
    document.querySelectorAll('.nav-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-tab') === tabName) {
            btn.classList.add('active');
        }
    });

    // 更新内容显示
    document.querySelectorAll('.tab-content').forEach(content => {
        content.classList.remove('active');
    });
    document.getElementById(tabName).classList.add('active');

    currentTab = tabName;

    // 根据当前标签页更新相关数据
    if (tabName === 'inventory') {
        updateInventoryDisplay();
        updateCategoryFilter();
    } else if (tabName === 'inbound') {
        updateProductNameSuggestions();
        updateSpecificationSuggestions();
    } else if (tabName === 'outbound') {
        updateOutboundInterface();
    } else if (tabName === 'transactions') {
        updateTransactionDisplay();
    } else if (tabName === 'developer') {
        updateDeveloperInfo();
    }
}

// 合并相同名称和规格的商品
function mergeInventoryItems(items) {
    if (!items || items.length === 0) {
        return [];
    }

    const mergedMap = new Map();

    items.forEach((item) => {
        // 确保商品有必要的属性
        if (!item.name || !item.unit) {
            console.warn('商品缺少必要属性:', item);
            return;
        }

        const key = `${item.name}|${item.specification || ''}|${item.unit}`;

        if (mergedMap.has(key)) {
            const existing = mergedMap.get(key);
            // 合并数量
            existing.quantity += item.quantity;
            // 取最低库存的最大值
            existing.minStock = Math.max(existing.minStock, item.minStock);
            // 合并编号（显示多个编号）
            existing.codes = existing.codes || [existing.code];
            existing.codes.push(item.code);
            // 更新最后更新时间为最新的
            if (new Date(item.lastUpdated) > new Date(existing.lastUpdated)) {
                existing.lastUpdated = item.lastUpdated;
            }
            // 合并备注
            if (item.remark && existing.remark !== item.remark) {
                existing.remark = existing.remark ? `${existing.remark}; ${item.remark}` : item.remark;
            }
            // 确保合并后的对象有正确的方法
            if (!existing.getStatus) {
                Object.setPrototypeOf(existing, InventoryItem.prototype);
            }
        } else {
            // 创建副本以避免修改原始数据，并确保继承InventoryItem的方法
            const merged = Object.assign(new InventoryItem(), item);
            merged.codes = [item.code];
            mergedMap.set(key, merged);
        }
    });

    return Array.from(mergedMap.values());
}

// 更新库存显示
function updateInventoryDisplay() {
    updateInventoryStats();
    updateInventoryTable();
}

// 更新库存统计
function updateInventoryStats() {
    const mergedItems = mergeInventoryItems(inventory);
    const totalItems = mergedItems.length;
    const totalQuantity = mergedItems.reduce((sum, item) => sum + item.quantity, 0);
    const lowStockCount = mergedItems.filter(item => item.getStatus() === 'low' || item.getStatus() === 'out').length;

    document.getElementById('totalItems').textContent = totalItems;
    document.getElementById('totalQuantity').textContent = totalQuantity;
    document.getElementById('lowStockCount').textContent = lowStockCount;
}

// 商品智能排序函数
function sortInventoryByNameAndSpec(items) {
    return items.sort((a, b) => {
        // 首先按商品名称排序
        if (a.name !== b.name) {
            return a.name.localeCompare(b.name, 'zh-CN');
        }
        // 名称相同时按规格排序
        if (a.specification !== b.specification) {
            return a.specification.localeCompare(b.specification, 'zh-CN');
        }
        // 规格也相同时按编号排序
        return a.code.localeCompare(b.code, 'zh-CN');
    });
}

// 更新库存表格
function updateInventoryTable(filteredInventory = null) {
    const tbody = document.getElementById('inventoryTableBody');
    let items = filteredInventory || inventory;

    // 合并相同名称和规格的商品
    const mergedItems = mergeInventoryItems(items);

    // 对商品进行智能排序
    const sortedItems = sortInventoryByNameAndSpec([...mergedItems]);

    tbody.innerHTML = '';

    if (sortedItems.length === 0) {
        tbody.innerHTML = '<tr><td colspan="9" style="text-align: center; padding: 20px;">暂无库存数据</td></tr>';
        // 更新全选复选框状态
        updateSelectAllCheckbox();
        return;
    }

    let currentProductName = '';

    sortedItems.forEach((item, index) => {
        const row = document.createElement('tr');
        const status = item.getStatus();

        // 检查是否是新的商品名称组
        const isNewGroup = item.name !== currentProductName;
        if (isNewGroup) {
            currentProductName = item.name;
            row.classList.add('product-group-start');
        }

        // 为相同名称的商品添加分组样式
        const sameNameItems = sortedItems.filter(i => i.name === item.name);
        if (sameNameItems.length > 1) {
            row.classList.add('product-group-member');
        }

        // 显示编号信息
        const codeDisplay = item.codes && item.codes.length > 1
            ? `${item.codes[0]} 等${item.codes.length}个`
            : item.code;

        // 库存数量显示优化
        const quantityDisplay = item.quantity;
        const quantityClass = status === 'out' ? 'text-danger' : status === 'low' ? 'text-warning' : 'text-success';

        // 检查是否被选中
        const isSelected = selectedItems.some(selected => selected.code === item.code);
        if (isSelected) {
            row.classList.add('selected-row');
        }

        row.innerHTML = `
            <td title="${item.codes ? item.codes.join(', ') : item.code}">
                ${isEditMode ? `<input type="checkbox" class="item-checkbox" data-code="${item.code}" onchange="toggleItemSelection('${item.code}')" ${isSelected ? 'checked' : ''}>` : ''}
                ${codeDisplay}
            </td>
            <td>
                ${item.name}
                ${sameNameItems.length > 1 ? `<span class="group-indicator">${sameNameItems.findIndex(i => i.code === item.code || (i.codes && i.codes.includes(item.code))) + 1}/${sameNameItems.length}</span>` : ''}
            </td>
            <td>${item.specification || '-'}</td>
            <td>${item.unit}</td>
            <td><strong class="${quantityClass}">${quantityDisplay}</strong></td>
            <td>${item.minStock}</td>
            <td><span class="status-${status}">${item.getStatusText()}</span></td>
            <td>${item.lastUpdated}</td>
            <td>
                <button class="edit-action-btn" onclick="openEditModal('${item.code}')" ${!isEditMode ? 'disabled' : ''}>
                    ✏️ 编辑
                </button>
                ${item.codes && item.codes.length > 1 ? `
                <button class="detail-action-btn" onclick="showMergedItemDetails('${item.name}', '${item.specification}', '${item.unit}')" title="查看详细信息">
                    📋 详情
                </button>
                ` : ''}
            </td>
        `;

        tbody.appendChild(row);
    });

    // 更新全选复选框状态
    updateSelectAllCheckbox();
}

// 显示合并商品的详细信息
function showMergedItemDetails(name, specification, unit) {
    const relatedItems = inventory.filter(item =>
        item.name === name &&
        item.specification === specification &&
        item.unit === unit
    );

    if (relatedItems.length === 0) {
        showMessage('未找到相关商品信息', 'error');
        return;
    }

    let detailsHtml = `
        <div class="merged-item-details">
            <h4>📦 商品详细信息</h4>
            <div class="detail-summary">
                <p><strong>商品名称：</strong>${name}</p>
                <p><strong>规格：</strong>${specification}</p>
                <p><strong>单位：</strong>${unit}</p>
                <p><strong>总库存：</strong>${relatedItems.reduce((sum, item) => sum + item.quantity, 0)} ${unit}</p>
                <p><strong>商品数量：</strong>${relatedItems.length} 个不同编号</p>
            </div>
            <div class="detail-items">
                <h5>📋 详细清单：</h5>
                <table class="detail-table">
                    <thead>
                        <tr>
                            <th>编号</th>
                            <th>库存</th>
                            <th>最低库存</th>
                            <th>状态</th>
                            <th>最后更新</th>
                            <th>备注</th>
                        </tr>
                    </thead>
                    <tbody>
    `;

    relatedItems.forEach(item => {
        const status = item.getStatus();
        detailsHtml += `
            <tr>
                <td>${item.code}</td>
                <td>${item.quantity}</td>
                <td>${item.minStock}</td>
                <td><span class="status-${status}">${item.getStatusText()}</span></td>
                <td>${item.lastUpdated}</td>
                <td>${item.remark || '-'}</td>
            </tr>
        `;
    });

    detailsHtml += `
                    </tbody>
                </table>
            </div>
        </div>
    `;

    showMessage(detailsHtml, 'info', 8000);
}

// 更新商品分类筛选器
function updateCategoryFilter() {
    const categoryFilter = document.getElementById('categoryFilter');
    if (!categoryFilter) return;

    const categories = [...new Set(inventory.map(item => item.name))].sort();

    // 保存当前选择
    const currentValue = categoryFilter.value;

    categoryFilter.innerHTML = '<option value="">全部商品</option>';
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
    });

    // 恢复选择
    categoryFilter.value = currentValue;
}

// 搜索库存
function searchInventory() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    let filteredInventory = inventory;

    // 按搜索词筛选
    if (searchTerm) {
        filteredInventory = filteredInventory.filter(item =>
            item.code.toLowerCase().includes(searchTerm) ||
            item.name.toLowerCase().includes(searchTerm) ||
            item.specification.toLowerCase().includes(searchTerm)
        );
    }

    // 按商品分类筛选
    if (categoryFilter) {
        filteredInventory = filteredInventory.filter(item => item.name === categoryFilter);
    }

    // 按库存状态筛选
    if (statusFilter) {
        filteredInventory = filteredInventory.filter(item => item.getStatus() === statusFilter);
    }

    updateInventoryTable(filteredInventory);

    // 显示筛选结果统计
    const totalCount = inventory.length;
    const filteredCount = filteredInventory.length;
    if (filteredCount < totalCount) {
        showMessage(`筛选结果：显示 ${filteredCount} 项，共 ${totalCount} 项`, 'info', 2000);
    }
}

// 清除筛选
function clearFilters() {
    document.getElementById('searchInput').value = '';
    document.getElementById('categoryFilter').value = '';
    document.getElementById('statusFilter').value = '';
    updateInventoryTable();
    showMessage('已清除所有筛选条件', 'success', 1500);
}

// 切换编辑模式
function toggleEditMode() {
    isEditMode = !isEditMode;
    const btn = document.getElementById('editToggleBtn');
    const batchBtn = document.getElementById('batchEditBtn');
    const clearBtn = document.getElementById('clearSelectionBtn');
    const selectAllCheckbox = document.getElementById('selectAll');
    const selectAllLabel = document.getElementById('selectAllLabel');

    if (isEditMode) {
        btn.textContent = '🔓 锁定编辑';
        btn.classList.add('unlocked');
        batchBtn.style.display = 'inline-block';
        selectAllCheckbox.style.display = 'inline-block';
        selectAllLabel.textContent = '全选';
        showMessage('编辑模式已开启，可以修改商品信息和批量操作', 'warning');
    } else {
        btn.textContent = '🔒 解锁编辑';
        btn.classList.remove('unlocked');
        batchBtn.style.display = 'none';
        clearBtn.style.display = 'none';
        selectAllCheckbox.style.display = 'none';
        selectAllLabel.textContent = '商品编号';
        selectedItems = [];
        showMessage('编辑模式已关闭', 'success');
    }

    // 保持当前筛选状态，重新应用筛选
    searchInventory();
    updateBatchEditButton();
}

// 全选/取消全选
function toggleSelectAll() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const isChecked = selectAllCheckbox.checked;

    if (isChecked) {
        // 全选当前筛选显示的商品
        const currentDisplayedItems = getCurrentDisplayedItems();

        // 将当前显示的商品添加到选择列表中（避免重复）
        currentDisplayedItems.forEach(item => {
            const exists = selectedItems.some(selected => selected.code === item.code);
            if (!exists) {
                selectedItems.push(item);
            }
        });
    } else {
        // 取消选择当前显示的商品
        const currentDisplayedItems = getCurrentDisplayedItems();
        const displayedCodes = currentDisplayedItems.map(item => item.code);

        selectedItems = selectedItems.filter(selected =>
            !displayedCodes.includes(selected.code)
        );
    }

    // 保持当前筛选状态，重新应用筛选
    searchInventory();
    updateBatchEditButton();
}

// 获取当前筛选显示的商品
function getCurrentDisplayedItems() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    let filteredInventory = inventory;

    // 按搜索词筛选
    if (searchTerm) {
        filteredInventory = filteredInventory.filter(item =>
            item.code.toLowerCase().includes(searchTerm) ||
            item.name.toLowerCase().includes(searchTerm) ||
            item.specification.toLowerCase().includes(searchTerm)
        );
    }

    // 按商品分类筛选
    if (categoryFilter) {
        filteredInventory = filteredInventory.filter(item => item.name === categoryFilter);
    }

    // 按库存状态筛选
    if (statusFilter) {
        filteredInventory = filteredInventory.filter(item => item.getStatus() === statusFilter);
    }

    // 合并相同名称和规格的商品
    const mergedItems = mergeInventoryItems(filteredInventory);

    // 对商品进行智能排序
    return sortInventoryByNameAndSpec([...mergedItems]);
}

// 切换单个商品选择
function toggleItemSelection(itemCode) {
    const currentDisplayedItems = getCurrentDisplayedItems();
    const item = currentDisplayedItems.find(i => i.code === itemCode);

    if (!item) return;

    const index = selectedItems.findIndex(selected => selected.code === itemCode);

    if (index > -1) {
        // 取消选择
        selectedItems.splice(index, 1);
    } else {
        // 添加选择
        selectedItems.push(item);
    }

    // 保持当前筛选状态，重新应用筛选
    searchInventory();
    updateBatchEditButton();
}

// 更新全选复选框状态
function updateSelectAllCheckbox() {
    const selectAllCheckbox = document.getElementById('selectAll');
    const selectAllLabel = document.getElementById('selectAllLabel');
    const currentDisplayedItems = getCurrentDisplayedItems();

    if (currentDisplayedItems.length === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
        selectAllLabel.textContent = '全选';
        return;
    }

    // 计算当前显示商品中有多少被选中
    const displayedCodes = currentDisplayedItems.map(item => item.code);
    const selectedDisplayedCount = selectedItems.filter(selected =>
        displayedCodes.includes(selected.code)
    ).length;

    if (selectedDisplayedCount === 0) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = false;
        selectAllLabel.textContent = `全选 (${currentDisplayedItems.length})`;
    } else if (selectedDisplayedCount === currentDisplayedItems.length) {
        selectAllCheckbox.indeterminate = false;
        selectAllCheckbox.checked = true;
        selectAllLabel.textContent = `取消全选 (${currentDisplayedItems.length})`;
    } else {
        selectAllCheckbox.indeterminate = true;
        selectAllCheckbox.checked = false;
        selectAllLabel.textContent = `已选 ${selectedDisplayedCount}/${currentDisplayedItems.length}`;
    }
}

// 更新批量编辑按钮状态
function updateBatchEditButton() {
    const batchBtn = document.getElementById('batchEditBtn');
    const clearBtn = document.getElementById('clearSelectionBtn');

    if (selectedItems.length > 0) {
        batchBtn.textContent = `📝 批量修改 (${selectedItems.length})`;
        batchBtn.disabled = false;
        clearBtn.style.display = 'inline-block';
        clearBtn.textContent = `❌ 清除选择 (${selectedItems.length})`;
    } else {
        batchBtn.textContent = '📝 批量修改';
        batchBtn.disabled = true;
        clearBtn.style.display = 'none';
    }
}

// 清除所有选择
function clearAllSelections() {
    selectedItems = [];
    // 保持当前筛选状态，重新应用筛选
    searchInventory();
    updateBatchEditButton();
    showMessage('已清除所有选择', 'success', 1500);
}

// 显示批量编辑模态框
function showBatchEditModal() {
    if (selectedItems.length === 0) {
        showMessage('请先选择要修改的商品', 'warning');
        return;
    }

    updateSelectedItemsList();
    updateEditModeHint();
    updatePreview();

    const modal = document.getElementById('batchEditModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 关闭批量编辑模态框
function closeBatchEditModal() {
    const modal = document.getElementById('batchEditModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 更新选中商品列表
function updateSelectedItemsList() {
    const container = document.getElementById('selectedItemsList');

    if (selectedItems.length === 0) {
        container.innerHTML = '<p>未选择任何商品</p>';
        return;
    }

    container.innerHTML = selectedItems.map(item => `
        <div class="selected-item">
            <span>${item.name} (${item.specification || '无规格'}) - 当前最低库存: ${item.minStock}</span>
            <span class="item-code">${item.code}</span>
        </div>
    `).join('');
}

// 设置批量编辑值
function setBatchValue(value) {
    document.getElementById('batchEditValue').value = value;
    updatePreview();
}

// 更新编辑模式提示
function updateEditModeHint() {
    const editModes = document.querySelectorAll('input[name="editMode"]');
    const valueHint = document.getElementById('valueHint');

    editModes.forEach(radio => {
        radio.addEventListener('change', function() {
            switch(this.value) {
                case 'setValue':
                    valueHint.textContent = '设置最低库存为此值';
                    break;
                case 'addValue':
                    valueHint.textContent = '在当前最低库存基础上增加此值';
                    break;
                case 'multiplyValue':
                    valueHint.textContent = '将当前最低库存乘以此倍数';
                    break;
            }
            updatePreview();
        });
    });

    // 监听数值变化
    document.getElementById('batchEditValue').addEventListener('input', updatePreview);
}

// 更新预览
function updatePreview() {
    const editMode = document.querySelector('input[name="editMode"]:checked').value;
    const value = parseFloat(document.getElementById('batchEditValue').value) || 0;
    const previewContainer = document.getElementById('previewResults');

    if (selectedItems.length === 0) {
        previewContainer.innerHTML = '<p>未选择任何商品</p>';
        return;
    }

    const previews = selectedItems.map(item => {
        let newMinStock;

        switch(editMode) {
            case 'setValue':
                newMinStock = value;
                break;
            case 'addValue':
                newMinStock = item.minStock + value;
                break;
            case 'multiplyValue':
                newMinStock = Math.round(item.minStock * value);
                break;
            default:
                newMinStock = item.minStock;
        }

        newMinStock = Math.max(0, newMinStock); // 确保不为负数

        return `
            <div class="preview-item">
                <span>${item.name} (${item.specification || '无规格'})</span>
                <span class="preview-change">${item.minStock} → ${newMinStock}</span>
            </div>
        `;
    });

    previewContainer.innerHTML = previews.join('');
}

// 执行批量编辑
function executeBatchEdit() {
    const editMode = document.querySelector('input[name="editMode"]:checked').value;
    const value = parseFloat(document.getElementById('batchEditValue').value);

    if (isNaN(value)) {
        showMessage('请输入有效的数值', 'error');
        return;
    }

    if (selectedItems.length === 0) {
        showMessage('未选择任何商品', 'warning');
        return;
    }

    let successCount = 0;
    const changes = [];

    selectedItems.forEach(selectedItem => {
        // 找到原始库存中对应的商品
        const originalItems = inventory.filter(item =>
            item.name === selectedItem.name &&
            item.specification === selectedItem.specification &&
            item.unit === selectedItem.unit
        );

        originalItems.forEach(item => {
            const oldMinStock = item.minStock;
            let newMinStock;

            switch(editMode) {
                case 'setValue':
                    newMinStock = value;
                    break;
                case 'addValue':
                    newMinStock = item.minStock + value;
                    break;
                case 'multiplyValue':
                    newMinStock = Math.round(item.minStock * value);
                    break;
                default:
                    newMinStock = item.minStock;
            }

            newMinStock = Math.max(0, newMinStock); // 确保不为负数

            if (newMinStock !== oldMinStock) {
                item.minStock = newMinStock;
                item.lastUpdated = new Date().toLocaleString('zh-CN');

                // 记录变更
                changes.push(`${item.name}: ${oldMinStock} → ${newMinStock}`);
                successCount++;

                // 添加交易记录
                addTransactionRecord('inbound', item.code, item.name, 0, item.unit, '最低库存调整',
                    `批量修改最低库存: ${oldMinStock} → ${newMinStock}`);
            }
        });
    });

    if (successCount > 0) {
        // 保存数据
        saveInventoryData();
        updateInventoryDisplay();
        updateCategoryFilter();

        // 清空选择
        selectedItems = [];
        updateBatchEditButton();
        updateSelectAllCheckbox();

        // 关闭模态框
        closeBatchEditModal();

        // 显示成功消息
        showMessage(`批量修改完成！共修改了 ${successCount} 个商品的最低库存`, 'success');

        // 可选：显示详细变更信息
        if (changes.length <= 10) {
            setTimeout(() => {
                showMessage(`变更详情：\n${changes.join('\n')}`, 'info', 5000);
            }, 1000);
        }
    } else {
        showMessage('没有商品需要修改', 'warning');
    }
}

// 打开编辑对话框
function openEditModal(productCode) {
    const item = inventory.find(i => i.code === productCode);
    if (!item) return;

    // 填充所有字段
    document.getElementById('editProductCode').value = item.code;
    document.getElementById('editProductName').value = item.name;
    document.getElementById('editSpecification').value = item.specification;
    document.getElementById('editUnit').value = item.unit;
    document.getElementById('editQuantity').value = item.quantity;
    document.getElementById('editMinStock').value = item.minStock;
    document.getElementById('editRemark').value = item.remark;

    const modal = document.getElementById('editModal');
    modal.classList.add('show');
    modal.style.display = 'flex';

    // 保存当前编辑的商品代码
    modal.dataset.productCode = productCode;
}

// 关闭编辑对话框
function closeEditModal() {
    const modal = document.getElementById('editModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 处理编辑
function handleEdit(event) {
    event.preventDefault();

    const modal = document.getElementById('editModal');
    const productCode = modal.dataset.productCode;
    const newName = document.getElementById('editProductName').value.trim();
    const newSpecification = document.getElementById('editSpecification').value.trim();
    const newUnit = document.getElementById('editUnit').value.trim();
    const newQuantity = parseInt(document.getElementById('editQuantity').value);
    const newMinStock = parseInt(document.getElementById('editMinStock').value);
    const newRemark = document.getElementById('editRemark').value.trim();

    if (!newName || !newUnit || newQuantity < 0 || newMinStock < 0) {
        showMessage('请填写有效的商品信息', 'error');
        return;
    }

    const item = inventory.find(i => i.code === productCode);
    if (!item) {
        showMessage('商品不存在', 'error');
        return;
    }

    // 记录原始值
    const oldName = item.name;
    const oldSpecification = item.specification;
    const oldUnit = item.unit;
    const oldQuantity = item.quantity;
    const oldMinStock = item.minStock;
    const oldRemark = item.remark;

    // 检查是否有实际修改
    const hasChanges = oldName !== newName ||
                      oldSpecification !== newSpecification ||
                      oldUnit !== newUnit ||
                      oldQuantity !== newQuantity ||
                      oldMinStock !== newMinStock ||
                      oldRemark !== newRemark;

    if (!hasChanges) {
        showMessage('没有检测到任何修改', 'warning');
        return;
    }

    // 更新商品信息
    item.name = newName;
    item.specification = newSpecification;
    item.unit = newUnit;
    item.quantity = newQuantity;
    item.minStock = newMinStock;
    item.remark = newRemark;
    item.lastUpdated = new Date().toLocaleString('zh-CN');

    // 记录修改历史
    const changes = [];
    if (oldName !== newName) changes.push(`名称: ${oldName} → ${newName}`);
    if (oldSpecification !== newSpecification) changes.push(`规格: ${oldSpecification} → ${newSpecification}`);
    if (oldUnit !== newUnit) changes.push(`单位: ${oldUnit} → ${newUnit}`);
    if (oldMinStock !== newMinStock) changes.push(`最低库存: ${oldMinStock} → ${newMinStock}`);
    if (oldRemark !== newRemark) changes.push(`备注: ${oldRemark} → ${newRemark}`);

    if (oldQuantity !== newQuantity) {
        const quantityDiff = newQuantity - oldQuantity;
        const type = quantityDiff > 0 ? 'inbound' : 'outbound';
        const reason = quantityDiff > 0 ? '库存调整(增加)' : '库存调整(减少)';
        addTransactionRecord(type, productCode, newName, Math.abs(quantityDiff), newUnit, reason, `库存调整: ${oldQuantity} → ${newQuantity}`);
    }

    // 如果有其他信息修改，也记录一条交易记录
    if (changes.length > 0) {
        addTransactionRecord('inbound', productCode, newName, 0, newUnit, '商品信息修改', `修改内容: ${changes.join(', ')}`);
    }

    saveInventoryData();
    updateInventoryDisplay();
    updateOutboundInterface();
    updateProductNameSuggestions();
    updateSpecificationSuggestions();
    updateComboPackageCards(); // 更新套餐状态
    closeEditModal();

    showMessage(`商品 ${newName} 信息已更新`, 'success');
}

// 显示删除确认对话框
function showDeleteConfirm() {
    const modal = document.getElementById('editModal');
    const productCode = modal.dataset.productCode;
    const item = inventory.find(i => i.code === productCode);

    if (!item) {
        showMessage('商品不存在', 'error');
        return;
    }

    // 填充商品信息
    const productInfo = document.getElementById('deleteProductInfo');
    productInfo.innerHTML = `
        <div class="product-info-item">
            <span class="product-info-label">商品编号:</span>
            <span class="product-info-value">${item.code}</span>
        </div>
        <div class="product-info-item">
            <span class="product-info-label">商品名称:</span>
            <span class="product-info-value">${item.name}</span>
        </div>
        <div class="product-info-item">
            <span class="product-info-label">规格:</span>
            <span class="product-info-value">${item.specification}</span>
        </div>
        <div class="product-info-item">
            <span class="product-info-label">当前库存:</span>
            <span class="product-info-value">${item.quantity} ${item.unit}</span>
        </div>
    `;

    // 分析删除影响
    const impacts = analyzeDeleteImpact(item);
    const impactList = document.getElementById('deleteImpactList');
    impactList.innerHTML = '';

    impacts.forEach(impact => {
        const li = document.createElement('li');
        li.textContent = impact;
        impactList.appendChild(li);
    });

    // 重置确认复选框
    document.getElementById('deleteConfirmCheckbox').checked = false;
    document.getElementById('confirmDeleteBtn').disabled = true;

    // 保存要删除的商品代码
    const deleteModal = document.getElementById('deleteConfirmModal');
    deleteModal.dataset.productCode = productCode;

    // 显示删除确认对话框
    deleteModal.classList.add('show');
    deleteModal.style.display = 'flex';
}

// 分析删除影响
function analyzeDeleteImpact(item) {
    const impacts = [];

    // 检查库存影响
    if (item.quantity > 0) {
        impacts.push(`将丢失 ${item.quantity} ${item.unit} 的库存`);
    }

    // 检查套餐影响
    const affectedCombos = comboPackages.filter(combo =>
        combo.items.some(comboItem => comboItem.code === item.code)
    );

    if (affectedCombos.length > 0) {
        impacts.push(`将影响 ${affectedCombos.length} 个组合套餐的可用性`);
        affectedCombos.forEach(combo => {
            impacts.push(`  - 套餐"${combo.name}"将不可用`);
        });
    }

    // 检查交易记录影响
    const relatedTransactions = transactionHistory.filter(trans => trans.productCode === item.code);
    if (relatedTransactions.length > 0) {
        impacts.push(`相关的 ${relatedTransactions.length} 条交易记录将保留，但商品信息可能显示异常`);
    }

    // 检查出库清单影响
    const cartItem = outboundCart.find(cartItem => cartItem.code === item.code);
    if (cartItem) {
        impacts.push(`当前出库清单中的此商品将被自动移除`);
    }

    if (impacts.length === 0) {
        impacts.push('删除此商品不会对系统造成其他影响');
    }

    return impacts;
}

// 关闭删除确认对话框
function closeDeleteConfirmModal() {
    const modal = document.getElementById('deleteConfirmModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 确认复选框变化事件
document.addEventListener('DOMContentLoaded', function() {
    const checkbox = document.getElementById('deleteConfirmCheckbox');
    const confirmBtn = document.getElementById('confirmDeleteBtn');

    if (checkbox && confirmBtn) {
        checkbox.addEventListener('change', function() {
            confirmBtn.disabled = !this.checked;
        });
    }
});

// 执行删除
function executeDelete() {
    const modal = document.getElementById('deleteConfirmModal');
    const productCode = modal.dataset.productCode;
    const item = inventory.find(i => i.code === productCode);

    if (!item) {
        showMessage('商品不存在', 'error');
        return;
    }

    const itemName = item.name;
    const itemQuantity = item.quantity;
    const itemUnit = item.unit;

    // 从库存中删除商品
    const index = inventory.findIndex(i => i.code === productCode);
    inventory.splice(index, 1);

    // 记录删除操作
    addTransactionRecord('outbound', productCode, itemName, itemQuantity, itemUnit, '商品删除', `商品已从系统中删除，原库存: ${itemQuantity} ${itemUnit}`);

    // 从出库清单中移除（如果存在）
    const cartIndex = outboundCart.findIndex(cartItem => cartItem.code === productCode);
    if (cartIndex > -1) {
        outboundCart.splice(cartIndex, 1);
        updateCartDisplay();
    }

    // 更新相关套餐状态
    updateComboPackageCards();

    // 保存数据并更新显示
    saveInventoryData();
    updateInventoryDisplay();
    updateOutboundInterface();
    updateProductNameSuggestions();

    // 关闭所有对话框
    closeDeleteConfirmModal();
    closeEditModal();

    showMessage(`商品 ${itemName} 已成功删除`, 'success');
}

// 确认对话框相关变量
let pendingAction = null;
let pendingActionData = null;

// 显示确认对话框
function showConfirmModal(message, action, data) {
    document.getElementById('confirmMessage').textContent = message;
    pendingAction = action;
    pendingActionData = data;

    const modal = document.getElementById('confirmModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 关闭确认对话框
function closeConfirmModal() {
    const modal = document.getElementById('confirmModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
        pendingAction = null;
        pendingActionData = null;
    }, 300);
}

// 执行确认的操作
function executeConfirmedAction() {
    try {
        if (pendingAction && pendingActionData) {
            pendingAction(pendingActionData);
        }
    } catch (error) {
        console.error('执行确认操作时发生错误:', error);
        showMessage('操作执行失败，请重试', 'error');
    } finally {
        // 无论操作是否成功，都要关闭确认对话框
        closeConfirmModal();
    }
}

// 处理入库
function handleInbound(event) {
    event.preventDefault();

    const code = document.getElementById('inProductCode').value.trim();
    const name = document.getElementById('inProductName').value.trim();
    const specification = document.getElementById('inSpecification').value.trim();
    const unit = document.getElementById('inUnit').value.trim();
    const quantity = parseInt(document.getElementById('inQuantity').value);
    const minStock = parseInt(document.getElementById('inMinStock').value);
    const remark = document.getElementById('inRemark').value.trim();

    // 验证数据
    if (!code || !name || !unit || quantity <= 0) {
        showMessage('请填写完整的商品信息', 'error');
        return;
    }

    // 检查是否已存在相同名称和规格的商品
    const existingItem = inventory.find(item =>
        item.name === name &&
        item.specification === specification &&
        item.unit === unit
    );

    const actionText = existingItem
        ? `发现相同商品："${name}" (${specification})，确认合并入库 ${quantity} ${unit}？\n当前库存：${existingItem.quantity} ${unit}，入库后将变为：${existingItem.quantity + quantity} ${unit}`
        : `确认添加新商品 "${name}" (${specification})，数量：${quantity} ${unit}？`;

    showConfirmModal(actionText, executeInbound, {
        code, name, specification, unit, quantity, minStock, remark, existingItem, form: event.target
    });
}

// 执行入库操作
function executeInbound(data) {
    try {
        const { code, name, specification, unit, quantity, minStock, remark, existingItem, form } = data;

        if (existingItem) {
            // 更新现有商品库存（合并相同名称和规格的商品）
            existingItem.quantity += quantity;
            existingItem.lastUpdated = new Date().toLocaleString('zh-CN');
            // 更新最低库存为较大值
            existingItem.minStock = Math.max(existingItem.minStock, minStock);
            // 合并备注信息
            if (remark && existingItem.remark !== remark) {
                existingItem.remark = existingItem.remark ? `${existingItem.remark}; ${remark}` : remark;
            }
            addTransactionRecord('inbound', existingItem.code, name, quantity, unit, '商品入库(合并)', remark);
            showMessage(`商品 ${name} (${specification}) 合并入库成功，当前库存：${existingItem.quantity} ${unit}`, 'success');
        } else {
            // 添加新商品
            const newItem = new InventoryItem(code, name, specification, unit, quantity, minStock, remark);
            inventory.push(newItem);
            addTransactionRecord('inbound', code, name, quantity, unit, '新商品入库', remark);
            showMessage(`新商品 ${name} (${specification}) 添加成功，库存：${quantity} ${unit}`, 'success');
        }

        // 保存数据并更新显示
        saveInventoryData();
        updateInventoryDisplay();
        updateOutboundInterface();
        updateProductNameSuggestions();
        updateSpecificationSuggestions();

        // 重置表单并重新生成商品编号
        if (form && typeof form.reset === 'function') {
            form.reset();
            // 清空商品编号，等待用户输入商品名称时自动生成
            document.getElementById('inProductCode').value = '';
        }
    } catch (error) {
        console.error('入库操作失败:', error);
        showMessage('入库操作失败，请重试', 'error');
    }
}

// 更新出库界面
function updateOutboundInterface() {
    if (outboundMode === 'single') {
        updateProductCategories();
        updateQuickSelectCards();
        clearSelectedProduct();
    } else {
        updateBatchInterface();
    }
}

// 更新商品分类
function updateProductCategories() {
    const categoryFilter = document.getElementById('productCategoryFilter');
    const categories = [...new Set(inventory.map(item => item.name))].sort();

    categoryFilter.innerHTML = '<option value="">全部商品</option>';
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
    });
}

// 更新快速选择卡片
function updateQuickSelectCards() {
    const container = document.getElementById('quickSelectCards');
    const categoryFilter = document.getElementById('productCategoryFilter').value;

    let availableItems = inventory.filter(item => item.quantity > 0);

    if (categoryFilter) {
        availableItems = availableItems.filter(item => item.name === categoryFilter);
    }

    // 按名称分组，每组最多显示前3个
    const groupedItems = {};
    availableItems.forEach(item => {
        if (!groupedItems[item.name]) {
            groupedItems[item.name] = [];
        }
        if (groupedItems[item.name].length < 3) {
            groupedItems[item.name].push(item);
        }
    });

    container.innerHTML = '';

    Object.values(groupedItems).flat().forEach(item => {
        const card = createQuickSelectCard(item);
        container.appendChild(card);
    });

    if (container.children.length === 0) {
        container.innerHTML = '<div style="text-align: center; color: #999; grid-column: 1/-1;">暂无可用商品</div>';
    }
}

// 创建快速选择卡片
function createQuickSelectCard(item) {
    const card = document.createElement('div');
    card.className = 'quick-select-card';
    card.dataset.productCode = item.code;

    const stockClass = item.getStatus() === 'normal' ? 'stock-normal' :
                      item.getStatus() === 'low' ? 'stock-low' : 'stock-out';

    card.innerHTML = `
        <div class="card-name">${item.name}</div>
        <div class="card-details">${item.code} | ${item.specification}</div>
        <div class="card-stock ${stockClass}">库存：${item.quantity} ${item.unit}</div>
    `;

    card.addEventListener('click', () => selectProduct(item.code));

    return card;
}

// 处理商品搜索
function handleProductSearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();

    if (searchTerm.length < 1) {
        hideSearchResults();
        return;
    }

    const availableItems = inventory.filter(item =>
        item.quantity > 0 && (
            item.name.toLowerCase().includes(searchTerm) ||
            item.code.toLowerCase().includes(searchTerm) ||
            item.specification.toLowerCase().includes(searchTerm)
        )
    );

    displaySearchResults(availableItems);
}

// 显示搜索结果
function displaySearchResults(items) {
    const container = document.getElementById('productSearchResults');

    if (items.length === 0) {
        container.innerHTML = '<div class="search-result-item"><div class="result-name">未找到匹配的商品</div></div>';
    } else {
        container.innerHTML = '';
        items.slice(0, 8).forEach(item => { // 最多显示8个结果
            const resultItem = createSearchResultItem(item);
            container.appendChild(resultItem);
        });
    }

    container.classList.add('show');
}

// 创建搜索结果项
function createSearchResultItem(item) {
    const div = document.createElement('div');
    div.className = 'search-result-item';
    div.dataset.productCode = item.code;

    const stockClass = item.getStatus() === 'normal' ? 'stock-normal' :
                      item.getStatus() === 'low' ? 'stock-low' : 'stock-out';

    div.innerHTML = `
        <div class="result-name">${item.name}</div>
        <div class="result-details">编号：${item.code} | 规格：${item.specification}</div>
        <div class="result-stock ${stockClass}">库存：${item.quantity} ${item.unit}</div>
    `;

    div.addEventListener('click', () => {
        selectProduct(item.code);
        hideSearchResults();
        document.getElementById('outProductSearch').value = '';
    });

    return div;
}

// 显示搜索结果
function showSearchResults() {
    const searchTerm = document.getElementById('outProductSearch').value.toLowerCase().trim();
    if (searchTerm.length >= 1) {
        handleProductSearch({ target: { value: searchTerm } });
    }
}

// 隐藏搜索结果
function hideSearchResults() {
    document.getElementById('productSearchResults').classList.remove('show');
}

// 选择商品
function selectProduct(productCode) {
    const item = inventory.find(i => i.code === productCode);
    if (!item || item.quantity <= 0) {
        showMessage('商品不存在或库存不足', 'error');
        return;
    }

    // 更新隐藏字段
    document.getElementById('selectedProductCode').value = productCode;

    // 更新显示区域
    const infoContainer = document.getElementById('selectedProductInfo');
    infoContainer.className = 'selected-product-info has-selection';

    const stockClass = item.getStatus() === 'normal' ? 'stock-normal' :
                      item.getStatus() === 'low' ? 'stock-low' : 'stock-out';

    infoContainer.innerHTML = `
        <div class="selected-product-details">
            <div class="selected-name">${item.name}</div>
            <div class="selected-info">编号：${item.code} | 规格：${item.specification} | 单位：${item.unit}</div>
            <div class="selected-stock ${stockClass}">可用库存：${item.quantity} ${item.unit}</div>
        </div>
    `;

    // 更新快速选择卡片的选中状态
    document.querySelectorAll('.quick-select-card').forEach(card => {
        card.classList.remove('selected');
        if (card.dataset.productCode === productCode) {
            card.classList.add('selected');
        }
    });

    // 设置出库数量的最大值
    const quantityInput = document.getElementById('outQuantity');
    quantityInput.max = item.quantity;
    quantityInput.placeholder = `最多可出库 ${item.quantity} ${item.unit}`;
}

// 清空选中的商品
function clearSelectedProduct() {
    document.getElementById('selectedProductCode').value = '';
    const infoContainer = document.getElementById('selectedProductInfo');
    infoContainer.className = 'selected-product-info';
    infoContainer.innerHTML = '<span class="no-selection">请先选择商品</span>';

    document.querySelectorAll('.quick-select-card').forEach(card => {
        card.classList.remove('selected');
    });

    const quantityInput = document.getElementById('outQuantity');
    quantityInput.max = '';
    quantityInput.placeholder = '请先选择商品';
}

// 出库模式切换
function switchOutboundMode(mode) {
    outboundMode = mode;

    // 更新按钮状态
    document.querySelectorAll('.mode-btn').forEach(btn => {
        btn.classList.remove('active');
        if (btn.getAttribute('data-mode') === mode) {
            btn.classList.add('active');
        }
    });

    // 切换界面显示
    const singleContainer = document.querySelector('.form-container');
    const batchContainer = document.getElementById('batchOutboundContainer');

    if (mode === 'single') {
        singleContainer.style.display = 'block';
        batchContainer.style.display = 'none';
        clearSelectedProduct();
    } else {
        singleContainer.style.display = 'none';
        batchContainer.style.display = 'grid';
        updateBatchInterface();
    }
}

// 更新批量出库界面
function updateBatchInterface() {
    updateBatchProductCategories();
    updateBatchQuickSelectCards();
    updateComboPackageCards();
    updateCartDisplay();
}

// 更新批量模式的商品分类
function updateBatchProductCategories() {
    const categoryFilter = document.getElementById('batchCategoryFilter');
    const categories = [...new Set(inventory.map(item => item.name))].sort();

    categoryFilter.innerHTML = '<option value="">全部商品</option>';
    categories.forEach(category => {
        const option = document.createElement('option');
        option.value = category;
        option.textContent = category;
        categoryFilter.appendChild(option);
    });
}

// 更新批量模式的快速选择卡片
function updateBatchQuickSelectCards() {
    const container = document.getElementById('batchQuickSelectCards');
    const categoryFilter = document.getElementById('batchCategoryFilter').value;

    let availableItems = inventory.filter(item => item.quantity > 0);

    if (categoryFilter) {
        availableItems = availableItems.filter(item => item.name === categoryFilter);
    }

    // 按名称分组，每组最多显示前3个
    const groupedItems = {};
    availableItems.forEach(item => {
        if (!groupedItems[item.name]) {
            groupedItems[item.name] = [];
        }
        if (groupedItems[item.name].length < 3) {
            groupedItems[item.name].push(item);
        }
    });

    container.innerHTML = '';

    Object.values(groupedItems).flat().forEach(item => {
        const card = createBatchQuickSelectCard(item);
        container.appendChild(card);
    });

    if (container.children.length === 0) {
        container.innerHTML = '<div style="text-align: center; color: #999; grid-column: 1/-1;">暂无可用商品</div>';
    }
}

// 创建批量模式的快速选择卡片
function createBatchQuickSelectCard(item) {
    const card = document.createElement('div');
    card.className = 'quick-select-card';
    card.dataset.productCode = item.code;

    const stockClass = item.getStatus() === 'normal' ? 'stock-normal' :
                      item.getStatus() === 'low' ? 'stock-low' : 'stock-out';

    // 检查是否已在购物车中
    const cartItem = outboundCart.find(cartItem => cartItem.code === item.code);
    const availableQuantity = item.quantity - (cartItem ? cartItem.quantity : 0);

    card.innerHTML = `
        <button class="add-to-cart-btn" onclick="addToCart('${item.code}')" ${availableQuantity <= 0 ? 'disabled' : ''}>
            +
        </button>
        <div class="card-name">${item.name}</div>
        <div class="card-details">${item.code} | ${item.specification}</div>
        <div class="card-stock ${stockClass}">
            库存：${item.quantity} ${item.unit}
            ${cartItem ? `<br><small>已选：${cartItem.quantity}</small>` : ''}
        </div>
    `;

    return card;
}

// 处理批量模式商品搜索
function handleBatchProductSearch(event) {
    const searchTerm = event.target.value.toLowerCase().trim();

    if (searchTerm.length < 1) {
        document.getElementById('batchSearchResults').classList.remove('show');
        return;
    }

    const availableItems = inventory.filter(item =>
        item.quantity > 0 && (
            item.name.toLowerCase().includes(searchTerm) ||
            item.code.toLowerCase().includes(searchTerm) ||
            item.specification.toLowerCase().includes(searchTerm)
        )
    );

    displayBatchSearchResults(availableItems);
}

// 显示批量模式搜索结果
function displayBatchSearchResults(items) {
    const container = document.getElementById('batchSearchResults');

    if (items.length === 0) {
        container.innerHTML = '<div class="search-result-item"><div class="result-name">未找到匹配的商品</div></div>';
    } else {
        container.innerHTML = '';
        items.slice(0, 8).forEach(item => {
            const resultItem = createBatchSearchResultItem(item);
            container.appendChild(resultItem);
        });
    }

    container.classList.add('show');
}

// 创建批量模式搜索结果项
function createBatchSearchResultItem(item) {
    const div = document.createElement('div');
    div.className = 'search-result-item';
    div.dataset.productCode = item.code;

    const stockClass = item.getStatus() === 'normal' ? 'stock-normal' :
                      item.getStatus() === 'low' ? 'stock-low' : 'stock-out';

    const cartItem = outboundCart.find(cartItem => cartItem.code === item.code);

    div.innerHTML = `
        <div class="result-name">${item.name}</div>
        <div class="result-details">编号：${item.code} | 规格：${item.specification}</div>
        <div class="result-stock ${stockClass}">
            库存：${item.quantity} ${item.unit}
            ${cartItem ? ` | 已选：${cartItem.quantity}` : ''}
        </div>
    `;

    div.addEventListener('click', () => {
        addToCart(item.code);
        document.getElementById('batchSearchResults').classList.remove('show');
        document.getElementById('batchProductSearch').value = '';
    });

    return div;
}

// 购物车管理功能

// 添加商品到购物车
function addToCart(productCode) {
    const item = inventory.find(i => i.code === productCode);
    if (!item || item.quantity <= 0) {
        showMessage('商品不存在或库存不足', 'error');
        return;
    }

    // 检查是否已在购物车中
    const existingCartItem = outboundCart.find(cartItem => cartItem.code === productCode);

    if (existingCartItem) {
        // 检查是否还有可用库存
        if (existingCartItem.quantity >= item.quantity) {
            showMessage('已达到最大可出库数量', 'warning');
            return;
        }
        existingCartItem.quantity += 1;
    } else {
        // 添加新商品到购物车
        outboundCart.push({
            code: item.code,
            name: item.name,
            specification: item.specification,
            unit: item.unit,
            quantity: 1,
            maxQuantity: item.quantity
        });
    }

    updateCartDisplay();
    updateBatchQuickSelectCards();
    showMessage(`已添加 ${item.name} 到出库清单`, 'success');
}

// 从购物车移除商品
function removeFromCart(productCode) {
    const index = outboundCart.findIndex(item => item.code === productCode);
    if (index > -1) {
        const item = outboundCart[index];
        outboundCart.splice(index, 1);
        updateCartDisplay();
        updateBatchQuickSelectCards();
        showMessage(`已从出库清单移除 ${item.name}`, 'success');
    }
}

// 更新购物车中商品数量
function updateCartItemQuantity(productCode, newQuantity) {
    const cartItem = outboundCart.find(item => item.code === productCode);
    if (!cartItem) return;

    if (newQuantity <= 0) {
        removeFromCart(productCode);
        return;
    }

    if (newQuantity > cartItem.maxQuantity) {
        showMessage('超出最大可出库数量', 'error');
        return;
    }

    cartItem.quantity = newQuantity;
    updateCartDisplay();
    updateBatchQuickSelectCards();
}

// 清空购物车
function clearCart() {
    if (outboundCart.length === 0) {
        showMessage('出库清单已为空', 'warning');
        return;
    }

    if (confirm('确认清空出库清单？')) {
        outboundCart = [];
        updateCartDisplay();
        updateBatchQuickSelectCards();
        showMessage('出库清单已清空', 'success');
    }
}

// 更新购物车显示
function updateCartDisplay() {
    const cartContainer = document.getElementById('cartItems');
    const cartCountElement = document.getElementById('cartItemCount');
    const cartTotalElement = document.getElementById('cartTotalItems');
    const submitBtn = document.getElementById('batchSubmitBtn');

    // 更新计数
    cartCountElement.textContent = outboundCart.length;
    cartTotalElement.textContent = outboundCart.length;

    // 更新提交按钮状态
    submitBtn.disabled = outboundCart.length === 0;

    if (outboundCart.length === 0) {
        cartContainer.innerHTML = `
            <div class="empty-cart">
                <p>🛒 出库清单为空</p>
                <p>点击上方商品卡片的 "+" 按钮添加商品</p>
            </div>
        `;
        return;
    }

    cartContainer.innerHTML = '';
    outboundCart.forEach(cartItem => {
        const cartItemElement = createCartItemElement(cartItem);
        cartContainer.appendChild(cartItemElement);
    });
}

// 创建购物车商品元素
function createCartItemElement(cartItem) {
    const div = document.createElement('div');
    div.className = 'cart-item';
    div.dataset.productCode = cartItem.code;

    div.innerHTML = `
        <div class="cart-item-header">
            <div class="cart-item-name">${cartItem.name}</div>
            <button class="cart-item-remove" onclick="removeFromCart('${cartItem.code}')">×</button>
        </div>
        <div class="cart-item-details">
            ${cartItem.code} | ${cartItem.specification}
        </div>
        <div class="cart-item-quantity">
            <span>数量：</span>
            <div class="quantity-controls">
                <button class="quantity-btn" onclick="updateCartItemQuantity('${cartItem.code}', ${cartItem.quantity - 1})">-</button>
                <input type="number" class="quantity-input" value="${cartItem.quantity}"
                       min="1" max="${cartItem.maxQuantity}"
                       onchange="updateCartItemQuantity('${cartItem.code}', parseInt(this.value))">
                <button class="quantity-btn" onclick="updateCartItemQuantity('${cartItem.code}', ${cartItem.quantity + 1})"
                        ${cartItem.quantity >= cartItem.maxQuantity ? 'disabled' : ''}>+</button>
            </div>
            <span>${cartItem.unit}</span>
        </div>
    `;

    return div;
}

// 组合套餐相关功能

// 更新套餐卡片显示
function updateComboPackageCards() {
    const container = document.getElementById('comboPackageCards');
    const countElement = document.getElementById('availableComboCount');

    if (!container || !countElement) return;

    container.innerHTML = '';

    const availablePackages = comboPackages.filter(pkg => pkg.isAvailable(inventory));
    countElement.textContent = availablePackages.length;

    if (comboPackages.length === 0) {
        container.innerHTML = '<div style="text-align: center; color: #999; grid-column: 1/-1;">暂无套餐，请在开发者选项中创建</div>';
        return;
    }

    comboPackages.forEach(pkg => {
        const card = createComboPackageCard(pkg);
        container.appendChild(card);
    });
}

// 创建套餐卡片
function createComboPackageCard(pkg) {
    const card = document.createElement('div');
    const available = pkg.getAvailableQuantity(inventory);
    const status = pkg.getStatus(inventory);

    card.className = `combo-package-card ${status === 'unavailable' ? 'unavailable' : status === 'low' ? 'low-stock' : ''}`;
    card.dataset.comboId = pkg.id;

    // 获取套餐中的商品信息
    const itemsInfo = pkg.items.map(item => {
        const inventoryItem = inventory.find(inv => inv.code === item.code);
        return `${inventoryItem ? inventoryItem.name : item.code} × ${item.quantity}`;
    }).join(', ');

    card.innerHTML = `
        <div class="combo-card-header">
            <div>
                <div class="combo-card-name">${pkg.name}</div>
                <div class="combo-card-description">${pkg.description}</div>
            </div>
            <div class="combo-card-status">
                ${status === 'unavailable' ? '缺货' : status === 'low' ? '库存少' : '正常'}
            </div>
        </div>
        <div class="combo-card-items">
            <h5>包含商品:</h5>
            <div class="combo-item">${itemsInfo}</div>
        </div>
        <div class="combo-card-available">
            可用套餐: ${available} 套
        </div>
    `;

    if (status !== 'unavailable') {
        card.addEventListener('click', () => addComboToCart(pkg.id));
    }

    return card;
}

// 添加套餐到购物车
function addComboToCart(comboId) {
    const pkg = comboPackages.find(p => p.id === comboId);
    if (!pkg) {
        showMessage('套餐不存在', 'error');
        return;
    }

    // 检查套餐是否可用
    if (!pkg.isAvailable(inventory)) {
        const unavailableItems = pkg.getUnavailableItems(inventory);
        const itemNames = unavailableItems.map(item => {
            const inventoryItem = inventory.find(inv => inv.code === item.code);
            return inventoryItem ? inventoryItem.name : item.code;
        }).join(', ');
        showMessage(`套餐中以下商品库存不足：${itemNames}`, 'error');
        return;
    }

    // 检查购物车中是否有足够空间
    let canAdd = true;
    const conflicts = [];

    pkg.items.forEach(item => {
        const inventoryItem = inventory.find(inv => inv.code === item.code);
        const cartItem = outboundCart.find(cart => cart.code === item.code);
        const currentCartQuantity = cartItem ? cartItem.quantity : 0;

        if (currentCartQuantity + item.quantity > inventoryItem.quantity) {
            canAdd = false;
            conflicts.push(inventoryItem.name);
        }
    });

    if (!canAdd) {
        showMessage(`添加套餐会导致以下商品超出库存：${conflicts.join(', ')}`, 'error');
        return;
    }

    // 添加套餐中的所有商品到购物车
    pkg.items.forEach(item => {
        const inventoryItem = inventory.find(inv => inv.code === item.code);
        const existingCartItem = outboundCart.find(cart => cart.code === item.code);

        if (existingCartItem) {
            existingCartItem.quantity += item.quantity;
        } else {
            outboundCart.push({
                code: item.code,
                name: inventoryItem.name,
                specification: inventoryItem.specification,
                unit: inventoryItem.unit,
                quantity: item.quantity,
                maxQuantity: inventoryItem.quantity
            });
        }
    });

    updateCartDisplay();
    updateBatchQuickSelectCards();
    updateComboPackageCards();
    showMessage(`已添加套餐"${pkg.name}"到出库清单`, 'success');
}

// 批量出库确认和执行

// 显示批量出库确认对话框
function showBatchConfirmModal() {
    if (outboundCart.length === 0) {
        showMessage('出库清单为空', 'warning');
        return;
    }

    // 更新确认信息
    document.getElementById('confirmTotalTypes').textContent = outboundCart.length;

    // 生成商品清单
    const confirmList = document.getElementById('confirmItemsList');
    confirmList.innerHTML = '';

    outboundCart.forEach(cartItem => {
        const confirmItem = document.createElement('div');
        confirmItem.className = 'confirm-item';

        confirmItem.innerHTML = `
            <div class="confirm-item-info">
                <div class="confirm-item-name">${cartItem.name}</div>
                <div class="confirm-item-details">${cartItem.code} | ${cartItem.specification}</div>
            </div>
            <div class="confirm-item-quantity">${cartItem.quantity} ${cartItem.unit}</div>
        `;

        confirmList.appendChild(confirmItem);
    });

    // 重置表单
    document.getElementById('batchOutReason').value = '';
    document.getElementById('batchOutRemark').value = '';

    // 显示对话框
    const modal = document.getElementById('batchConfirmModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 关闭批量出库确认对话框
function closeBatchConfirmModal() {
    const modal = document.getElementById('batchConfirmModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 执行批量出库
function executeBatchOutbound() {
    const reason = document.getElementById('batchOutReason').value;
    const remark = document.getElementById('batchOutRemark').value.trim();

    if (!reason) {
        showMessage('请选择出库原因', 'error');
        return;
    }

    if (outboundCart.length === 0) {
        showMessage('出库清单为空', 'error');
        return;
    }

    // 验证库存是否足够
    const insufficientItems = [];
    outboundCart.forEach(cartItem => {
        const inventoryItem = inventory.find(item => item.code === cartItem.code);
        if (!inventoryItem || inventoryItem.quantity < cartItem.quantity) {
            insufficientItems.push(cartItem.name);
        }
    });

    if (insufficientItems.length > 0) {
        showMessage(`以下商品库存不足：${insufficientItems.join(', ')}`, 'error');
        return;
    }

    // 执行批量出库
    let successCount = 0;
    const batchRemark = `批量出库 - ${remark}`;

    outboundCart.forEach(cartItem => {
        const inventoryItem = inventory.find(item => item.code === cartItem.code);
        if (inventoryItem) {
            // 减少库存
            inventoryItem.quantity -= cartItem.quantity;
            inventoryItem.lastUpdated = new Date().toLocaleString('zh-CN');

            // 添加交易记录
            addTransactionRecord('outbound', cartItem.code, cartItem.name, cartItem.quantity, cartItem.unit, reason, batchRemark);

            successCount++;
        }
    });

    // 保存数据并更新显示
    saveInventoryData();
    updateInventoryDisplay();
    updateOutboundInterface();

    // 清空购物车
    outboundCart = [];
    updateCartDisplay();

    // 关闭对话框
    closeBatchConfirmModal();

    // 显示成功消息
    showMessage(`批量出库成功！共出库 ${successCount} 种商品`, 'success');
}

// 处理出库
function handleOutbound(event) {
    event.preventDefault();

    const productCode = document.getElementById('selectedProductCode').value;
    const quantity = parseInt(document.getElementById('outQuantity').value);
    const reason = document.getElementById('outReason').value;
    const remark = document.getElementById('outRemark').value.trim();

    // 验证数据
    if (!productCode || !quantity || quantity <= 0 || !reason) {
        showMessage('请填写完整的出库信息', 'error');
        return;
    }

    // 查找商品
    const item = inventory.find(i => i.code === productCode);
    if (!item) {
        showMessage('商品不存在', 'error');
        return;
    }

    // 检查库存是否足够
    if (item.quantity < quantity) {
        showMessage(`库存不足，当前库存：${item.quantity} ${item.unit}`, 'error');
        return;
    }

    const actionText = `确认为商品 "${item.name}" 出库 ${quantity} ${item.unit}？\n当前库存：${item.quantity} ${item.unit}，出库后将剩余：${item.quantity - quantity} ${item.unit}\n出库原因：${reason}`;

    showConfirmModal(actionText, executeOutbound, {
        item, quantity, reason, remark, form: event.target
    });
}

// 执行出库操作
function executeOutbound(data) {
    const { item, quantity, reason, remark, form } = data;

    // 执行出库
    item.quantity -= quantity;
    item.lastUpdated = new Date().toLocaleString('zh-CN');

    // 添加交易记录
    addTransactionRecord('outbound', item.code, item.name, quantity, item.unit, reason, remark);

    // 保存数据并更新显示
    saveInventoryData();
    updateInventoryDisplay();
    updateOutboundInterface();

    showMessage(`商品 ${item.name} 出库成功，出库数量：${quantity} ${item.unit}，剩余库存：${item.quantity} ${item.unit}`, 'success');

    // 重置表单
    form.reset();
    clearSelectedProduct();
}

// 更新交易明细显示
function updateTransactionDisplay(filteredTransactions = null) {
    const tbody = document.getElementById('transactionTableBody');
    const transactions = filteredTransactions || transactionHistory;

    tbody.innerHTML = '';

    if (transactions.length === 0) {
        tbody.innerHTML = '<tr><td colspan="8" style="text-align: center; color: #999;">暂无交易记录</td></tr>';
        return;
    }

    transactions.forEach(transaction => {
        const row = document.createElement('tr');
        const typeClass = transaction.type === 'inbound' ? 'transaction-inbound' : 'transaction-outbound';

        row.innerHTML = `
            <td>${transaction.timestamp}</td>
            <td><span class="${typeClass}">${transaction.getTypeText()}</span></td>
            <td>${transaction.productCode}</td>
            <td>${transaction.productName}</td>
            <td>${transaction.quantity}</td>
            <td>${transaction.unit}</td>
            <td>${transaction.reason}${transaction.remark ? ' - ' + transaction.remark : ''}</td>
            <td>${transaction.operator}</td>
        `;

        tbody.appendChild(row);
    });
}

// 搜索交易记录
function searchTransactions() {
    const searchTerm = document.getElementById('transactionSearchInput').value.toLowerCase().trim();
    const typeFilter = document.getElementById('transactionTypeFilter').value;

    let filteredTransactions = transactionHistory;

    // 按类型筛选
    if (typeFilter) {
        filteredTransactions = filteredTransactions.filter(t => t.type === typeFilter);
    }

    // 按搜索词筛选
    if (searchTerm) {
        filteredTransactions = filteredTransactions.filter(t =>
            t.productCode.toLowerCase().includes(searchTerm) ||
            t.productName.toLowerCase().includes(searchTerm) ||
            t.reason.toLowerCase().includes(searchTerm) ||
            t.remark.toLowerCase().includes(searchTerm)
        );
    }

    updateTransactionDisplay(filteredTransactions);
}

// 导出交易记录
function exportTransactions() {
    if (transactionHistory.length === 0) {
        showMessage('暂无交易记录可导出', 'warning');
        return;
    }

    const headers = ['时间', '类型', '商品编号', '商品名称', '数量', '单位', '原因', '备注', '操作员'];
    const csvContent = [
        headers.join(','),
        ...transactionHistory.map(t => [
            t.timestamp,
            t.getTypeText(),
            t.productCode,
            t.productName,
            t.quantity,
            t.unit,
            t.reason,
            t.remark,
            t.operator
        ].join(','))
    ].join('\n');

    const blob = new Blob(['\ufeff' + csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `出入库明细_${new Date().toLocaleDateString('zh-CN')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showMessage('交易记录导出成功', 'success');
}

// 开发者模式相关功能

// 处理标题点击（激活开发者模式）
function handleTitleClick() {
    titleClickCount++;

    if (titleClickTimer) {
        clearTimeout(titleClickTimer);
    }

    titleClickTimer = setTimeout(() => {
        if (titleClickCount >= 5) {
            if (!developerMode) {
                activateDeveloperMode();
            } else {
                deactivateDeveloperMode();
            }
        }
        titleClickCount = 0;
    }, 2000);
}

// 激活开发者模式
function activateDeveloperMode() {
    developerMode = true;
    document.querySelector('.developer-btn').style.display = 'block';
    showMessage('🔧 开发者模式已激活！', 'warning');
}

// 关闭开发者模式
function deactivateDeveloperMode() {
    developerMode = false;
    document.querySelector('.developer-btn').style.display = 'none';
    if (currentTab === 'developer') {
        switchTab('inventory');
    }
    showMessage('开发者模式已关闭', 'success');
}

// 更新开发者信息
function updateDeveloperInfo() {
    document.getElementById('devInventoryCount').textContent = inventory.length;
    document.getElementById('devTransactionCount').textContent = transactionHistory.length;
    document.getElementById('devComboCount').textContent = comboPackages.length;

    // 计算存储大小
    const inventorySize = JSON.stringify(inventory).length;
    const transactionSize = JSON.stringify(transactionHistory).length;
    const comboSize = JSON.stringify(comboPackages).length;
    const totalSize = (inventorySize + transactionSize + comboSize) / 1024;
    document.getElementById('devStorageSize').textContent = totalSize.toFixed(2) + ' KB';

    // 获取最后备份时间
    const lastBackup = localStorage.getItem('lastBackupTime');
    document.getElementById('devLastBackup').textContent = lastBackup || '从未备份';
}

// 创建数据备份
function createBackup() {
    const backupData = {
        inventory: inventory,
        transactionHistory: transactionHistory,
        comboPackages: comboPackages,
        timestamp: new Date().toLocaleString('zh-CN'),
        version: '1.0'
    };

    // 保存到localStorage作为最新备份
    localStorage.setItem('warehouseBackup', JSON.stringify(backupData));
    localStorage.setItem('lastBackupTime', backupData.timestamp);

    // 下载备份文件
    const blob = new Blob([JSON.stringify(backupData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `仓库数据备份_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.json`;
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    updateDeveloperInfo();
    showMessage('数据备份成功！', 'success');
}

// 导出所有数据
function exportAllData() {
    const exportData = {
        inventory: inventory,
        transactionHistory: transactionHistory,
        comboPackages: comboPackages,
        systemInfo: {
            exportTime: new Date().toLocaleString('zh-CN'),
            totalItems: inventory.length,
            totalTransactions: transactionHistory.length,
            totalCombos: comboPackages.length,
            version: '1.0'
        }
    };

    const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `仓库完整数据_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.json`;
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showMessage('数据导出成功！', 'success');
}

// 显示还原模态框
function showRestoreModal() {
    const modal = document.getElementById('restoreModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 关闭还原模态框
function closeRestoreModal() {
    const modal = document.getElementById('restoreModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
        document.getElementById('restoreFileInput').value = '';
    }, 300);
}

// 从文件还原数据
function restoreFromFile() {
    const fileInput = document.getElementById('restoreFileInput');
    const file = fileInput.files[0];

    if (!file) {
        showMessage('请选择备份文件', 'error');
        return;
    }

    const reader = new FileReader();
    reader.onload = function(e) {
        try {
            const backupData = JSON.parse(e.target.result);

            // 验证备份数据格式
            if (!backupData.inventory || !backupData.transactionHistory) {
                showMessage('备份文件格式不正确', 'error');
                return;
            }

            // 确认还原
            const comboCount = backupData.comboPackages ? backupData.comboPackages.length : 0;
            const confirmText = `确认还原数据？\n备份时间：${backupData.timestamp || '未知'}\n库存商品：${backupData.inventory.length} 个\n交易记录：${backupData.transactionHistory.length} 条\n组合套餐：${comboCount} 个\n\n当前数据将被覆盖！`;

            if (confirm(confirmText)) {
                // 还原数据
                inventory = backupData.inventory.map(item => Object.assign(new InventoryItem(), item));
                transactionHistory = backupData.transactionHistory.map(item => Object.assign(new TransactionRecord(), item));
                comboPackages = backupData.comboPackages ? backupData.comboPackages.map(item => Object.assign(new ComboPackage(), item)) : [];

                // 保存到localStorage
                saveInventoryData();
                saveTransactionHistory();
                saveComboPackages();

                // 更新所有显示
                updateInventoryDisplay();
                updateOutboundInterface();
                updateProductNameSuggestions();
                updateTransactionDisplay();
                updateDeveloperInfo();

                closeRestoreModal();
                showMessage('数据还原成功！', 'success');
            }
        } catch (error) {
            showMessage('备份文件解析失败：' + error.message, 'error');
        }
    };

    reader.readAsText(file);
}

// 显示初始化模态框
function showInitializeModal() {
    const modal = document.getElementById('initializeModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 关闭初始化模态框
function closeInitializeModal() {
    const modal = document.getElementById('initializeModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 备份后初始化
function initializeWithBackup() {
    if (inventory.length > 0 || transactionHistory.length > 0) {
        createBackup();
        setTimeout(() => {
            performInitialization();
            closeInitializeModal();
        }, 1000);
    } else {
        performInitialization();
        closeInitializeModal();
    }
}

// 直接初始化
function initializeWithoutBackup() {
    if (confirm('确认直接初始化？当前数据将被清空且无法恢复！')) {
        performInitialization();
        closeInitializeModal();
    }
}

// 执行初始化
function performInitialization() {
    // 清空现有数据
    inventory = [];
    transactionHistory = [];

    // 添加示例数据（包含相同名称和规格的商品用于测试合并功能）
    const sampleInventory = [
        new InventoryItem('AP001', '苹果', '红富士', '箱', 50, 10, '新鲜水果'),
        new InventoryItem('AP002', '苹果', '青苹果', '箱', 30, 10, '新鲜水果'),
        new InventoryItem('AP003', '苹果', '红富士', '箱', 25, 10, '新鲜水果-批次2'), // 相同名称和规格
        new InventoryItem('BN001', '香蕉', '进口', '箱', 25, 5, '热带水果'),
        new InventoryItem('BN002', '香蕉', '进口', '箱', 15, 5, '热带水果-批次2'), // 相同名称和规格
        new InventoryItem('OR001', '橙子', '脐橙', '箱', 40, 15, '维C丰富'),
        new InventoryItem('OR002', '橙子', '血橙', '箱', 20, 10, '维C丰富'),
        new InventoryItem('OR003', '橙子', '脐橙', '箱', 30, 15, '维C丰富-批次2'), // 相同名称和规格
        new InventoryItem('PR001', '梨', '雪花梨', '箱', 35, 8, '清甜多汁'),
        new InventoryItem('GP001', '葡萄', '巨峰', '箱', 15, 5, '粒大饱满'),
        new InventoryItem('WM001', '西瓜', '无籽', '个', 100, 20, '夏季水果')
    ];

    // 添加示例交易记录
    const sampleTransactions = [
        new TransactionRecord('inbound', 'AP001', '苹果', 50, '箱', '初始库存', '系统初始化数据'),
        new TransactionRecord('inbound', 'BN001', '香蕉', 25, '箱', '初始库存', '系统初始化数据'),
        new TransactionRecord('outbound', 'AP001', '苹果', 5, '箱', '销售', '客户订单'),
        new TransactionRecord('inbound', 'OR001', '橙子', 40, '箱', '新货入库', '供应商送货')
    ];

    inventory = sampleInventory;
    transactionHistory = sampleTransactions;

    // 保存数据
    saveInventoryData();
    saveTransactionHistory();

    // 更新所有显示
    updateInventoryDisplay();
    updateOutboundInterface();
    updateProductNameSuggestions();
    updateTransactionDisplay();
    updateDeveloperInfo();

    showMessage('系统初始化完成！已添加示例数据', 'success');
}

// 显示清空数据模态框
function showClearDataModal() {
    const modal = document.getElementById('clearDataModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 关闭清空数据模态框
function closeClearDataModal() {
    const modal = document.getElementById('clearDataModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 备份后清空
function clearDataWithBackup() {
    if (inventory.length > 0 || transactionHistory.length > 0) {
        createBackup();
        setTimeout(() => {
            performClearData();
            closeClearDataModal();
        }, 1000);
    } else {
        showMessage('没有数据需要清空', 'warning');
        closeClearDataModal();
    }
}

// 直接清空
function clearDataWithoutBackup() {
    if (confirm('确认直接清空所有数据？此操作无法撤销！')) {
        performClearData();
        closeClearDataModal();
    }
}

// 执行清空数据
function performClearData() {
    inventory = [];
    transactionHistory = [];

    // 清空localStorage
    localStorage.removeItem('warehouseInventory');
    localStorage.removeItem('warehouseTransactions');

    // 更新所有显示
    updateInventoryDisplay();
    updateOutboundInterface();
    updateProductNameSuggestions();
    updateTransactionDisplay();
    updateDeveloperInfo();

    showMessage('所有数据已清空', 'success');
}

// 套餐管理功能

// 显示套餐管理对话框
function showComboManageModal() {
    updateComboList();
    const modal = document.getElementById('comboManageModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 关闭套餐管理对话框
function closeComboManageModal() {
    const modal = document.getElementById('comboManageModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 更新套餐列表
function updateComboList() {
    const container = document.getElementById('comboList');
    container.innerHTML = '';

    if (comboPackages.length === 0) {
        container.innerHTML = '<div style="text-align: center; color: #999; padding: 40px;">暂无套餐</div>';
        return;
    }

    comboPackages.forEach(pkg => {
        const item = createComboListItem(pkg);
        container.appendChild(item);
    });
}

// 创建套餐列表项
function createComboListItem(pkg) {
    const div = document.createElement('div');
    div.className = 'combo-list-item';

    const itemsInfo = pkg.items.map(item => {
        const inventoryItem = inventory.find(inv => inv.code === item.code);
        return `${inventoryItem ? inventoryItem.name : item.code} × ${item.quantity}`;
    }).join(', ');

    const available = pkg.getAvailableQuantity(inventory);

    div.innerHTML = `
        <div class="combo-info">
            <div class="combo-info-name">${pkg.name}</div>
            <div class="combo-info-description">${pkg.description}</div>
            <div class="combo-info-items">包含: ${itemsInfo}</div>
            <div class="combo-info-items">可用套餐: ${available} 套</div>
        </div>
        <div class="combo-actions">
            <button class="combo-action-btn edit" onclick="editCombo('${pkg.id}')">编辑</button>
            <button class="combo-action-btn delete" onclick="deleteCombo('${pkg.id}')">删除</button>
        </div>
    `;

    return div;
}

// 显示创建套餐对话框
function showCreateComboModal() {
    document.getElementById('comboModalTitle').textContent = '🎁 创建套餐';
    document.getElementById('comboForm').reset();
    document.getElementById('comboItemsList').innerHTML = '';

    // 更新商品选择列表
    updateComboItemSelect();

    const modal = document.getElementById('createComboModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
    modal.dataset.editingId = '';
}

// 关闭创建套餐对话框
function closeCreateComboModal() {
    const modal = document.getElementById('createComboModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 更新套餐商品选择列表
function updateComboItemSelect() {
    const select = document.getElementById('comboItemSelect');
    select.innerHTML = '<option value="">选择商品</option>';

    inventory.forEach(item => {
        const option = document.createElement('option');
        option.value = item.code;
        option.textContent = `${item.name} (${item.code}) - 库存: ${item.quantity}`;
        select.appendChild(option);
    });
}

// 添加商品到套餐
function addComboItem() {
    const select = document.getElementById('comboItemSelect');
    const quantityInput = document.getElementById('comboItemQuantity');

    const productCode = select.value;
    const quantity = parseInt(quantityInput.value);

    if (!productCode || !quantity || quantity <= 0) {
        showMessage('请选择商品并输入有效数量', 'error');
        return;
    }

    const inventoryItem = inventory.find(item => item.code === productCode);
    if (!inventoryItem) {
        showMessage('商品不存在', 'error');
        return;
    }

    // 检查是否已添加
    const container = document.getElementById('comboItemsList');
    const existingItem = container.querySelector(`[data-code="${productCode}"]`);

    if (existingItem) {
        showMessage('商品已添加，请先删除后重新添加', 'warning');
        return;
    }

    // 添加到列表
    const itemElement = createComboItemElement(inventoryItem, quantity);
    container.appendChild(itemElement);

    // 重置选择
    select.value = '';
    quantityInput.value = '1';

    showMessage(`已添加 ${inventoryItem.name}`, 'success');
}

// 创建套餐商品元素
function createComboItemElement(inventoryItem, quantity) {
    const div = document.createElement('div');
    div.className = 'combo-item-entry';
    div.dataset.code = inventoryItem.code;
    div.dataset.quantity = quantity;

    div.innerHTML = `
        <div class="combo-item-info">
            <div class="combo-item-name">${inventoryItem.name}</div>
            <div class="combo-item-details">${inventoryItem.code} | 数量: ${quantity} ${inventoryItem.unit}</div>
        </div>
        <button class="combo-item-remove" onclick="removeComboItem('${inventoryItem.code}')">×</button>
    `;

    return div;
}

// 从套餐中移除商品
function removeComboItem(productCode) {
    const container = document.getElementById('comboItemsList');
    const item = container.querySelector(`[data-code="${productCode}"]`);
    if (item) {
        item.remove();
        showMessage('商品已移除', 'success');
    }
}

// 保存套餐
document.getElementById('comboForm').addEventListener('submit', function(event) {
    event.preventDefault();

    const name = document.getElementById('comboName').value.trim();
    const description = document.getElementById('comboDescription').value.trim();
    const modal = document.getElementById('createComboModal');
    const editingId = modal.dataset.editingId;

    if (!name) {
        showMessage('请输入套餐名称', 'error');
        return;
    }

    // 获取套餐商品
    const container = document.getElementById('comboItemsList');
    const itemElements = container.querySelectorAll('.combo-item-entry');

    if (itemElements.length === 0) {
        showMessage('请至少添加一个商品', 'error');
        return;
    }

    const items = Array.from(itemElements).map(element => ({
        code: element.dataset.code,
        quantity: parseInt(element.dataset.quantity)
    }));

    if (editingId) {
        // 编辑现有套餐
        const pkg = comboPackages.find(p => p.id === editingId);
        if (pkg) {
            pkg.name = name;
            pkg.description = description;
            pkg.items = items;
            pkg.updatedAt = new Date().toLocaleString('zh-CN');
            showMessage('套餐更新成功', 'success');
        }
    } else {
        // 创建新套餐
        const newPackage = new ComboPackage(null, name, description, items);
        comboPackages.push(newPackage);
        showMessage('套餐创建成功', 'success');
    }

    saveComboPackages();
    updateComboList();
    updateComboPackageCards();
    updateDeveloperInfo();
    closeCreateComboModal();
});

// 编辑套餐
function editCombo(comboId) {
    const pkg = comboPackages.find(p => p.id === comboId);
    if (!pkg) {
        showMessage('套餐不存在', 'error');
        return;
    }

    // 填充表单
    document.getElementById('comboModalTitle').textContent = '🎁 编辑套餐';
    document.getElementById('comboName').value = pkg.name;
    document.getElementById('comboDescription').value = pkg.description;

    // 更新商品选择列表
    updateComboItemSelect();

    // 填充商品列表
    const container = document.getElementById('comboItemsList');
    container.innerHTML = '';

    pkg.items.forEach(item => {
        const inventoryItem = inventory.find(inv => inv.code === item.code);
        if (inventoryItem) {
            const itemElement = createComboItemElement(inventoryItem, item.quantity);
            container.appendChild(itemElement);
        }
    });

    // 显示对话框
    const modal = document.getElementById('createComboModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
    modal.dataset.editingId = comboId;
}

// 删除套餐
function deleteCombo(comboId) {
    const pkg = comboPackages.find(p => p.id === comboId);
    if (!pkg) {
        showMessage('套餐不存在', 'error');
        return;
    }

    if (confirm(`确认删除套餐"${pkg.name}"？`)) {
        const index = comboPackages.findIndex(p => p.id === comboId);
        comboPackages.splice(index, 1);

        saveComboPackages();
        updateComboList();
        updateComboPackageCards();
        updateDeveloperInfo();

        showMessage('套餐删除成功', 'success');
    }
}

// 数据导出功能

// 显示导出配置对话框
function showExportModal() {
    updateExportCounts();
    const modal = document.getElementById('exportModal');
    modal.classList.add('show');
    modal.style.display = 'flex';
}

// 更新导出数量统计
function updateExportCounts() {
    const filteredData = getCurrentFilteredData();
    const totalData = inventory;

    // 如果没有筛选条件，筛选数据等于全部数据
    const filteredCount = hasActiveFilters() ? filteredData.length : totalData.length;
    const totalCount = totalData.length;

    // 更新显示
    const filteredCountElement = document.getElementById('filteredCount');
    const totalCountElement = document.getElementById('totalCount');

    if (hasActiveFilters()) {
        filteredCountElement.textContent = `当前筛选: ${filteredCount} 项`;
        // 默认选择筛选结果
        document.getElementById('exportFiltered').checked = true;
    } else {
        filteredCountElement.textContent = `当前显示: ${filteredCount} 项 (无筛选)`;
        // 如果没有筛选，默认选择全部
        document.getElementById('exportAll').checked = true;
    }

    totalCountElement.textContent = `全部库存: ${totalCount} 项`;
}

// 关闭导出配置对话框
function closeExportModal() {
    const modal = document.getElementById('exportModal');
    modal.classList.remove('show');
    setTimeout(() => {
        modal.style.display = 'none';
    }, 300);
}

// 执行导出
function executeExport() {
    const format = document.querySelector('input[name="exportFormat"]:checked').value;

    switch (format) {
        case 'excel':
            exportToExcel();
            break;
        case 'image':
            exportToImage();
            break;
        case 'pdf':
            exportToPDF();
            break;
        case 'csv':
            exportToCSV();
            break;
        default:
            showMessage('请选择导出格式', 'error');
            return;
    }

    closeExportModal();
}

// 获取导出配置
function getExportConfig() {
    return {
        exportScope: document.querySelector('input[name="exportScope"]:checked').value,
        includeStats: document.getElementById('includeStats').checked,
        includeTimestamp: document.getElementById('includeTimestamp').checked,
        onlyAvailable: document.getElementById('onlyAvailable').checked,
        includeLowStock: document.getElementById('includeLowStock').checked,
        sortBy: document.getElementById('exportSortBy').value
    };
}

// 准备导出数据
function prepareExportData() {
    const config = getExportConfig();
    let data;

    // 根据导出范围选择数据
    if (config.exportScope === 'filtered') {
        // 导出筛选结果
        data = getCurrentFilteredData();
        // 如果没有筛选条件，则使用全部数据
        if (data.length === 0 && !hasActiveFilters()) {
            data = [...inventory];
        }
    } else {
        // 导出全部数据
        data = [...inventory];
    }

    // 应用导出配置的额外筛选
    if (config.onlyAvailable) {
        data = data.filter(item => item.quantity > 0);
    }

    // 排序数据
    data = sortExportData(data, config.sortBy);

    return { data, config };
}

// 获取当前筛选的数据
function getCurrentFilteredData() {
    const searchTerm = document.getElementById('searchInput').value.toLowerCase().trim();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    let filteredInventory = inventory;

    // 按搜索词筛选
    if (searchTerm) {
        filteredInventory = filteredInventory.filter(item =>
            item.code.toLowerCase().includes(searchTerm) ||
            item.name.toLowerCase().includes(searchTerm) ||
            item.specification.toLowerCase().includes(searchTerm)
        );
    }

    // 按商品分类筛选
    if (categoryFilter) {
        filteredInventory = filteredInventory.filter(item => item.name === categoryFilter);
    }

    // 按库存状态筛选
    if (statusFilter) {
        filteredInventory = filteredInventory.filter(item => item.getStatus() === statusFilter);
    }

    return filteredInventory;
}

// 检查是否有活跃的筛选条件
function hasActiveFilters() {
    const searchTerm = document.getElementById('searchInput').value.trim();
    const categoryFilter = document.getElementById('categoryFilter').value;
    const statusFilter = document.getElementById('statusFilter').value;

    return searchTerm !== '' || categoryFilter !== '' || statusFilter !== '';
}

// 排序导出数据
function sortExportData(data, sortBy) {
    return data.sort((a, b) => {
        switch (sortBy) {
            case 'name':
                return a.name.localeCompare(b.name, 'zh-CN');
            case 'code':
                return a.code.localeCompare(b.code);
            case 'quantity':
                return b.quantity - a.quantity;
            case 'status':
                const statusOrder = { 'out': 0, 'low': 1, 'normal': 2 };
                return statusOrder[a.getStatus()] - statusOrder[b.getStatus()];
            case 'lastUpdated':
                return new Date(b.lastUpdated) - new Date(a.lastUpdated);
            default:
                return 0;
        }
    });
}

// Excel导出功能
function exportToExcel() {
    const { data, config } = prepareExportData();

    // 创建工作簿
    const wb = XLSX.utils.book_new();

    // 准备数据
    const wsData = [];

    // 添加标题
    if (config.includeTimestamp) {
        wsData.push(['库存数据导出报表']);
        wsData.push(['导出时间：' + new Date().toLocaleString('zh-CN')]);
        wsData.push([]);
    }

    // 添加统计信息
    if (config.includeStats) {
        const totalItems = data.length;
        const totalQuantity = data.reduce((sum, item) => sum + item.quantity, 0);
        const lowStockCount = data.filter(item => item.getStatus() === 'low' || item.getStatus() === 'out').length;

        wsData.push(['统计信息']);
        wsData.push(['商品种类总数', totalItems]);
        wsData.push(['库存总数量', totalQuantity]);
        wsData.push(['库存不足商品', lowStockCount]);
        wsData.push([]);
    }

    // 添加表头
    const headers = ['商品编号', '商品名称', '规格', '单位', '当前库存', '最低库存', '状态', '最后更新'];
    wsData.push(headers);

    // 添加数据行
    data.forEach(item => {
        const status = config.includeLowStock ?
            (item.getStatus() === 'out' ? '⚠️ 缺货' :
             item.getStatus() === 'low' ? '⚠️ 库存不足' : '✅ 正常') :
            item.getStatusText();

        wsData.push([
            item.code,
            item.name,
            item.specification,
            item.unit,
            item.quantity,
            item.minStock,
            status,
            item.lastUpdated
        ]);
    });

    // 创建工作表
    const ws = XLSX.utils.aoa_to_sheet(wsData);

    // 设置列宽
    ws['!cols'] = [
        { width: 12 }, // 商品编号
        { width: 20 }, // 商品名称
        { width: 15 }, // 规格
        { width: 8 },  // 单位
        { width: 12 }, // 当前库存
        { width: 12 }, // 最低库存
        { width: 12 }, // 状态
        { width: 20 }  // 最后更新
    ];

    // 添加工作表到工作簿
    XLSX.utils.book_append_sheet(wb, ws, '库存数据');

    // 导出文件
    const fileName = `库存数据_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.xlsx`;

    try {
        XLSX.writeFile(wb, fileName);
        showMessage('Excel文件导出成功！', 'success');
    } catch (error) {
        // 如果XLSX库不可用，使用CSV格式作为备选
        console.warn('XLSX库不可用，使用CSV格式导出');
        exportToCSV();
    }
}

// CSV导出功能
function exportToCSV() {
    const { data, config } = prepareExportData();

    let csvContent = '';

    // 添加BOM以支持中文
    csvContent = '\ufeff';

    // 添加标题和时间戳
    if (config.includeTimestamp) {
        csvContent += '库存数据导出报表\n';
        csvContent += `导出时间：${new Date().toLocaleString('zh-CN')}\n\n`;
    }

    // 添加统计信息
    if (config.includeStats) {
        const totalItems = data.length;
        const totalQuantity = data.reduce((sum, item) => sum + item.quantity, 0);
        const lowStockCount = data.filter(item => item.getStatus() === 'low' || item.getStatus() === 'out').length;

        csvContent += '统计信息\n';
        csvContent += `商品种类总数,${totalItems}\n`;
        csvContent += `库存总数量,${totalQuantity}\n`;
        csvContent += `库存不足商品,${lowStockCount}\n\n`;
    }

    // 添加表头
    const headers = ['商品编号', '商品名称', '规格', '单位', '当前库存', '最低库存', '状态', '最后更新'];
    csvContent += headers.join(',') + '\n';

    // 添加数据行
    data.forEach(item => {
        const status = config.includeLowStock ?
            (item.getStatus() === 'out' ? '缺货' :
             item.getStatus() === 'low' ? '库存不足' : '正常') :
            item.getStatusText();

        const row = [
            item.code,
            `"${item.name}"`, // 用引号包围以处理逗号
            `"${item.specification}"`,
            item.unit,
            item.quantity,
            item.minStock,
            status,
            `"${item.lastUpdated}"`
        ];
        csvContent += row.join(',') + '\n';
    });

    // 创建下载链接
    const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
    const link = document.createElement('a');
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `库存数据_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);

    showMessage('CSV文件导出成功！', 'success');
}

// 图片导出功能
function exportToImage() {
    const { data, config } = prepareExportData();

    // 创建一个临时的canvas元素
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    // 设置画布尺寸
    const padding = 40;
    const rowHeight = 35;
    const colWidths = [100, 150, 120, 60, 100, 100, 100, 160]; // 列宽
    const tableWidth = colWidths.reduce((sum, width) => sum + width, 0);

    let currentY = padding;

    // 计算所需高度
    let totalRows = 1; // 表头
    if (config.includeTimestamp) totalRows += 3;
    if (config.includeStats) totalRows += 5;
    totalRows += data.length;

    canvas.width = tableWidth + padding * 2;
    canvas.height = totalRows * rowHeight + padding * 2;

    // 设置背景色
    ctx.fillStyle = '#ffffff';
    ctx.fillRect(0, 0, canvas.width, canvas.height);

    // 设置字体
    ctx.font = '14px Microsoft YaHei, Arial, sans-serif';
    ctx.textAlign = 'left';
    ctx.textBaseline = 'middle';

    // 绘制标题
    if (config.includeTimestamp) {
        ctx.fillStyle = '#333333';
        ctx.font = 'bold 18px Microsoft YaHei, Arial, sans-serif';
        ctx.fillText('库存数据导出报表', padding, currentY + rowHeight / 2);
        currentY += rowHeight;

        ctx.font = '14px Microsoft YaHei, Arial, sans-serif';
        ctx.fillText(`导出时间：${new Date().toLocaleString('zh-CN')}`, padding, currentY + rowHeight / 2);
        currentY += rowHeight * 2;
    }

    // 绘制统计信息
    if (config.includeStats) {
        const totalItems = data.length;
        const totalQuantity = data.reduce((sum, item) => sum + item.quantity, 0);
        const lowStockCount = data.filter(item => item.getStatus() === 'low' || item.getStatus() === 'out').length;

        ctx.fillStyle = '#666666';
        ctx.font = 'bold 14px Microsoft YaHei, Arial, sans-serif';
        ctx.fillText('统计信息', padding, currentY + rowHeight / 2);
        currentY += rowHeight;

        ctx.font = '12px Microsoft YaHei, Arial, sans-serif';
        ctx.fillText(`商品种类总数：${totalItems}`, padding, currentY + rowHeight / 2);
        currentY += rowHeight;
        ctx.fillText(`库存总数量：${totalQuantity}`, padding, currentY + rowHeight / 2);
        currentY += rowHeight;
        ctx.fillText(`库存不足商品：${lowStockCount}`, padding, currentY + rowHeight / 2);
        currentY += rowHeight * 1.5;
    }

    // 绘制表格
    drawTableToCanvas(ctx, data, config, padding, currentY, colWidths, rowHeight);

    // 转换为图片并下载
    canvas.toBlob(function(blob) {
        const link = document.createElement('a');
        const url = URL.createObjectURL(blob);
        link.setAttribute('href', url);
        link.setAttribute('download', `库存数据_${new Date().toLocaleDateString('zh-CN').replace(/\//g, '-')}.png`);
        link.style.visibility = 'hidden';
        document.body.appendChild(link);
        link.click();
        document.body.removeChild(link);
        URL.revokeObjectURL(url);

        showMessage('图片文件导出成功！', 'success');
    }, 'image/png');
}

// 在Canvas上绘制表格
function drawTableToCanvas(ctx, data, config, startX, startY, colWidths, rowHeight) {
    const headers = ['商品编号', '商品名称', '规格', '单位', '当前库存', '最低库存', '状态', '最后更新'];

    let currentY = startY;

    // 绘制表头
    ctx.fillStyle = '#667eea';
    ctx.fillRect(startX, currentY, colWidths.reduce((sum, width) => sum + width, 0), rowHeight);

    ctx.fillStyle = '#ffffff';
    ctx.font = 'bold 12px Microsoft YaHei, Arial, sans-serif';

    let currentX = startX;
    headers.forEach((header, index) => {
        ctx.fillText(header, currentX + 8, currentY + rowHeight / 2);
        currentX += colWidths[index];
    });

    currentY += rowHeight;

    // 绘制数据行
    ctx.font = '11px Microsoft YaHei, Arial, sans-serif';

    data.forEach((item, rowIndex) => {
        // 交替行背景色
        if (rowIndex % 2 === 0) {
            ctx.fillStyle = '#f8f9ff';
            ctx.fillRect(startX, currentY, colWidths.reduce((sum, width) => sum + width, 0), rowHeight);
        }

        // 根据库存状态设置文字颜色
        const status = item.getStatus();
        ctx.fillStyle = status === 'out' ? '#dc3545' : status === 'low' ? '#ffc107' : '#333333';

        const statusText = config.includeLowStock ?
            (status === 'out' ? '⚠️ 缺货' :
             status === 'low' ? '⚠️ 库存不足' : '✅ 正常') :
            item.getStatusText();

        const rowData = [
            item.code,
            item.name.length > 12 ? item.name.substring(0, 12) + '...' : item.name,
            item.specification.length > 10 ? item.specification.substring(0, 10) + '...' : item.specification,
            item.unit,
            item.quantity.toString(),
            item.minStock.toString(),
            statusText,
            item.lastUpdated.length > 16 ? item.lastUpdated.substring(0, 16) + '...' : item.lastUpdated
        ];

        currentX = startX;
        rowData.forEach((cellData, colIndex) => {
            // 状态列使用特殊颜色
            if (colIndex === 6) {
                ctx.fillStyle = status === 'out' ? '#dc3545' : status === 'low' ? '#ffc107' : '#28a745';
            } else {
                ctx.fillStyle = '#333333';
            }

            ctx.fillText(cellData, currentX + 8, currentY + rowHeight / 2);
            currentX += colWidths[colIndex];
        });

        currentY += rowHeight;
    });

    // 绘制表格边框
    ctx.strokeStyle = '#dee2e6';
    ctx.lineWidth = 1;

    // 绘制水平线
    for (let i = 0; i <= data.length + 1; i++) {
        const y = startY + i * rowHeight;
        ctx.beginPath();
        ctx.moveTo(startX, y);
        ctx.lineTo(startX + colWidths.reduce((sum, width) => sum + width, 0), y);
        ctx.stroke();
    }

    // 绘制垂直线
    currentX = startX;
    for (let i = 0; i <= colWidths.length; i++) {
        ctx.beginPath();
        ctx.moveTo(currentX, startY);
        ctx.lineTo(currentX, startY + (data.length + 1) * rowHeight);
        ctx.stroke();
        if (i < colWidths.length) {
            currentX += colWidths[i];
        }
    }
}

// PDF导出功能
function exportToPDF() {
    const { data, config } = prepareExportData();

    // 创建一个简化的PDF内容（使用HTML转换）
    const printWindow = window.open('', '_blank');

    let htmlContent = `
        <!DOCTYPE html>
        <html>
        <head>
            <meta charset="UTF-8">
            <title>库存数据报表</title>
            <style>
                body {
                    font-family: 'Microsoft YaHei', Arial, sans-serif;
                    margin: 20px;
                    font-size: 12px;
                    line-height: 1.4;
                }
                .header {
                    text-align: center;
                    margin-bottom: 30px;
                    border-bottom: 2px solid #667eea;
                    padding-bottom: 15px;
                }
                .header h1 {
                    color: #667eea;
                    margin-bottom: 10px;
                    font-size: 24px;
                }
                .header .timestamp {
                    color: #666;
                    font-size: 14px;
                }
                .stats {
                    background: #f8f9ff;
                    padding: 15px;
                    border-radius: 8px;
                    margin-bottom: 20px;
                    border: 1px solid #e8ebff;
                }
                .stats h3 {
                    color: #667eea;
                    margin-bottom: 10px;
                }
                .stats-grid {
                    display: grid;
                    grid-template-columns: repeat(3, 1fr);
                    gap: 15px;
                }
                .stat-item {
                    text-align: center;
                    padding: 10px;
                    background: white;
                    border-radius: 5px;
                }
                .stat-value {
                    font-size: 18px;
                    font-weight: bold;
                    color: #667eea;
                }
                .stat-label {
                    font-size: 12px;
                    color: #666;
                    margin-top: 5px;
                }
                table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-top: 20px;
                    font-size: 11px;
                }
                th {
                    background: #667eea;
                    color: white;
                    padding: 10px 8px;
                    text-align: left;
                    font-weight: bold;
                }
                td {
                    padding: 8px;
                    border-bottom: 1px solid #eee;
                }
                tr:nth-child(even) {
                    background: #f8f9ff;
                }
                .status-normal { color: #28a745; font-weight: bold; }
                .status-low { color: #ffc107; font-weight: bold; }
                .status-out { color: #dc3545; font-weight: bold; }
                .footer {
                    margin-top: 30px;
                    text-align: center;
                    color: #666;
                    font-size: 10px;
                    border-top: 1px solid #eee;
                    padding-top: 15px;
                }
                @media print {
                    body { margin: 0; }
                    .no-print { display: none; }
                }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>📊 库存数据报表</h1>
    `;

    if (config.includeTimestamp) {
        htmlContent += `<div class="timestamp">导出时间：${new Date().toLocaleString('zh-CN')}</div>`;
    }

    htmlContent += `</div>`;

    // 添加统计信息
    if (config.includeStats) {
        const totalItems = data.length;
        const totalQuantity = data.reduce((sum, item) => sum + item.quantity, 0);
        const lowStockCount = data.filter(item => item.getStatus() === 'low' || item.getStatus() === 'out').length;

        htmlContent += `
            <div class="stats">
                <h3>📈 统计信息</h3>
                <div class="stats-grid">
                    <div class="stat-item">
                        <div class="stat-value">${totalItems}</div>
                        <div class="stat-label">商品种类</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${totalQuantity}</div>
                        <div class="stat-label">库存总量</div>
                    </div>
                    <div class="stat-item">
                        <div class="stat-value">${lowStockCount}</div>
                        <div class="stat-label">库存不足</div>
                    </div>
                </div>
            </div>
        `;
    }

    // 添加表格
    htmlContent += `
        <table>
            <thead>
                <tr>
                    <th>商品编号</th>
                    <th>商品名称</th>
                    <th>规格</th>
                    <th>单位</th>
                    <th>当前库存</th>
                    <th>最低库存</th>
                    <th>状态</th>
                    <th>最后更新</th>
                </tr>
            </thead>
            <tbody>
    `;

    data.forEach(item => {
        const status = item.getStatus();
        const statusClass = `status-${status}`;
        const statusText = config.includeLowStock ?
            (status === 'out' ? '⚠️ 缺货' :
             status === 'low' ? '⚠️ 库存不足' : '✅ 正常') :
            item.getStatusText();

        htmlContent += `
            <tr>
                <td>${item.code}</td>
                <td>${item.name}</td>
                <td>${item.specification}</td>
                <td>${item.unit}</td>
                <td>${item.quantity}</td>
                <td>${item.minStock}</td>
                <td class="${statusClass}">${statusText}</td>
                <td>${item.lastUpdated}</td>
            </tr>
        `;
    });

    htmlContent += `
            </tbody>
        </table>
        <div class="footer">
            <p>本报表由仓库库存管理系统自动生成 | 生成时间：${new Date().toLocaleString('zh-CN')}</p>
        </div>
        <div class="no-print" style="margin-top: 20px; text-align: center;">
            <button onclick="window.print()" style="background: #667eea; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer; margin-right: 10px;">🖨️ 打印</button>
            <button onclick="window.close()" style="background: #6c757d; color: white; border: none; padding: 10px 20px; border-radius: 5px; cursor: pointer;">关闭</button>
        </div>
        </body>
        </html>
    `;

    printWindow.document.write(htmlContent);
    printWindow.document.close();

    // 等待内容加载完成后自动打印
    printWindow.onload = function() {
        setTimeout(() => {
            printWindow.print();
        }, 500);
    };

    showMessage('PDF预览窗口已打开，请使用浏览器的打印功能保存为PDF', 'success');
}

// 快速导出函数（不显示配置对话框）
function quickExportToExcel() {
    // 设置默认配置
    document.getElementById('includeStats').checked = true;
    document.getElementById('includeTimestamp').checked = true;
    document.getElementById('onlyAvailable').checked = false;
    document.getElementById('includeLowStock').checked = true;
    document.getElementById('exportSortBy').value = 'name';

    // 根据当前筛选状态设置导出范围
    if (hasActiveFilters()) {
        document.getElementById('exportFiltered').checked = true;
    } else {
        document.getElementById('exportAll').checked = true;
    }

    exportToExcel();
}

function quickExportToImage() {
    // 设置默认配置
    document.getElementById('includeStats').checked = true;
    document.getElementById('includeTimestamp').checked = true;
    document.getElementById('onlyAvailable').checked = false;
    document.getElementById('includeLowStock').checked = true;
    document.getElementById('exportSortBy').value = 'name';

    // 根据当前筛选状态设置导出范围
    if (hasActiveFilters()) {
        document.getElementById('exportFiltered').checked = true;
    } else {
        document.getElementById('exportAll').checked = true;
    }

    exportToImage();
}

function quickExportToPDF() {
    // 设置默认配置
    document.getElementById('includeStats').checked = true;
    document.getElementById('includeTimestamp').checked = true;
    document.getElementById('onlyAvailable').checked = false;
    document.getElementById('includeLowStock').checked = true;
    document.getElementById('exportSortBy').value = 'name';

    // 根据当前筛选状态设置导出范围
    if (hasActiveFilters()) {
        document.getElementById('exportFiltered').checked = true;
    } else {
        document.getElementById('exportAll').checked = true;
    }

    exportToPDF();
}

function quickExportToCSV() {
    // 设置默认配置
    document.getElementById('includeStats').checked = true;
    document.getElementById('includeTimestamp').checked = true;
    document.getElementById('onlyAvailable').checked = false;
    document.getElementById('includeLowStock').checked = true;
    document.getElementById('exportSortBy').value = 'name';

    // 根据当前筛选状态设置导出范围
    if (hasActiveFilters()) {
        document.getElementById('exportFiltered').checked = true;
    } else {
        document.getElementById('exportAll').checked = true;
    }

    exportToCSV();
}

// 显示消息
function showMessage(message, type = 'success', duration = 3000) {
    const messageBox = document.getElementById('messageBox');

    // 清除之前的定时器（如果存在）
    if (messageBox.hideTimer) {
        clearTimeout(messageBox.hideTimer);
    }

    // 检查是否为HTML内容
    if (message.includes('<')) {
        messageBox.innerHTML = message;
    } else {
        messageBox.textContent = message;
    }

    messageBox.className = `message-box ${type} show`;

    // 设置新的定时器来隐藏消息
    messageBox.hideTimer = setTimeout(() => {
        messageBox.classList.remove('show');
        // 完全隐藏后清空内容和类名
        setTimeout(() => {
            messageBox.textContent = '';
            messageBox.innerHTML = '';
            messageBox.className = 'message-box';
        }, 300); // 等待动画完成
    }, duration);
}
