<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>仓库库存管理系统</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header class="header">
            <h1 id="systemTitle" onclick="handleTitleClick()">仓库库存管理系统</h1>
            <nav class="nav">
                <button class="nav-btn active" data-tab="inventory">库存查询</button>
                <button class="nav-btn" data-tab="inbound">入库管理</button>
                <button class="nav-btn" data-tab="outbound">出库管理</button>
                <button class="nav-btn" data-tab="transactions">出入库明细</button>
                <button class="nav-btn developer-btn" data-tab="developer" style="display: none;">🔧 开发者选项</button>
            </nav>
        </header>

        <main class="main">
            <!-- 库存查询页面 -->
            <div id="inventory" class="tab-content active">
                <div class="section-header">
                    <h2>库存查询</h2>
                    <div class="header-controls">
                        <div class="search-box">
                            <input type="text" id="searchInput" placeholder="搜索商品名称、编号或规格...">
                            <select id="categoryFilter">
                                <option value="">全部商品</option>
                            </select>
                            <select id="statusFilter">
                                <option value="">全部状态</option>
                                <option value="normal">库存正常</option>
                                <option value="low">库存不足</option>
                                <option value="out">缺货</option>
                            </select>
                            <button onclick="searchInventory()">搜索</button>
                            <button onclick="clearFilters()" class="clear-btn">清除筛选</button>
                            <button id="editToggleBtn" onclick="toggleEditMode()" class="edit-btn">🔒 解锁编辑</button>
                            <button id="batchEditBtn" onclick="showBatchEditModal()" class="batch-edit-btn" style="display: none;">📝 批量修改</button>
                            <button id="clearSelectionBtn" onclick="clearAllSelections()" class="clear-selection-btn" style="display: none;">❌ 清除选择</button>
                        </div>
                        <div class="export-controls">
                            <button onclick="showExportModal()" class="export-main-btn">📊 导出数据</button>
                            <div class="export-dropdown">
                                <button onclick="quickExportToExcel()" class="export-btn excel-btn">📗 Excel</button>
                                <button onclick="quickExportToImage()" class="export-btn image-btn">🖼️ 图片</button>
                                <button onclick="quickExportToPDF()" class="export-btn pdf-btn">📄 PDF</button>
                                <button onclick="quickExportToCSV()" class="export-btn csv-btn">📋 CSV</button>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="inventory-stats">
                    <div class="stat-card">
                        <h3>总商品种类</h3>
                        <span id="totalItems">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>总库存数量</h3>
                        <span id="totalQuantity">0</span>
                    </div>
                    <div class="stat-card">
                        <h3>低库存预警</h3>
                        <span id="lowStockCount">0</span>
                    </div>
                </div>
                <div class="table-container">
                    <table id="inventoryTable">
                        <thead>
                            <tr>
                                <th>
                                    <input type="checkbox" id="selectAll" onchange="toggleSelectAll()" style="display: none;">
                                    <span id="selectAllLabel">商品编号</span>
                                </th>
                                <th>商品名称</th>
                                <th>规格</th>
                                <th>单位</th>
                                <th>当前库存</th>
                                <th>最低库存</th>
                                <th>状态</th>
                                <th>最后更新</th>
                                <th>操作</th>
                            </tr>
                        </thead>
                        <tbody id="inventoryTableBody">
                            <!-- 动态生成内容 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 入库管理页面 -->
            <div id="inbound" class="tab-content">
                <div class="section-header">
                    <h2>入库管理</h2>
                </div>
                <div class="form-container">
                    <form id="inboundForm">
                        <div class="form-group">
                            <label for="inProductCode">商品编号:</label>
                            <input type="text" id="inProductCode" required readonly>
                            <small class="form-hint">编号将根据商品名称自动生成</small>
                        </div>
                        <div class="form-group">
                            <label for="inProductName">商品名称:</label>
                            <input type="text" id="inProductName" required list="productNameSuggestions">
                            <datalist id="productNameSuggestions">
                                <!-- 动态生成选项 -->
                            </datalist>
                            <small class="form-hint">选择现有名称可自动生成规格编号</small>
                        </div>
                        <div class="form-group">
                            <label for="inSpecification">规格:</label>
                            <input type="text" id="inSpecification" list="specificationSuggestions">
                            <datalist id="specificationSuggestions">
                                <!-- 动态生成选项 -->
                            </datalist>
                            <small class="form-hint">选择现有规格或输入新规格</small>
                        </div>
                        <div class="form-group">
                            <label for="inUnit">单位:</label>
                            <input type="text" id="inUnit" required>
                        </div>
                        <div class="form-group">
                            <label for="inQuantity">入库数量:</label>
                            <input type="number" id="inQuantity" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="inMinStock">最低库存:</label>
                            <input type="number" id="inMinStock" min="0" value="10">
                        </div>
                        <div class="form-group">
                            <label for="inRemark">备注:</label>
                            <textarea id="inRemark" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit">确认入库</button>
                            <button type="reset">重置表单</button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- 出库管理页面 -->
            <div id="outbound" class="tab-content">
                <div class="section-header">
                    <h2>出库管理</h2>
                    <div class="outbound-mode-switch">
                        <button class="mode-btn active" data-mode="single">单个出库</button>
                        <button class="mode-btn" data-mode="batch">批量出库</button>
                    </div>
                </div>
                <div class="form-container">
                    <!-- 商品搜索和筛选区域 -->
                    <div class="product-search-section">
                        <div class="search-controls">
                            <div class="form-group">
                                <label for="outProductSearch">搜索商品:</label>
                                <input type="text" id="outProductSearch" placeholder="输入商品名称、编号或规格搜索..." autocomplete="off">
                                <div id="productSearchResults" class="search-results"></div>
                            </div>
                            <div class="form-group">
                                <label for="productCategoryFilter">商品分类:</label>
                                <select id="productCategoryFilter">
                                    <option value="">全部商品</option>
                                </select>
                            </div>
                        </div>

                        <!-- 快速选择卡片 -->
                        <div class="quick-select-section">
                            <h4>常用商品快选</h4>
                            <div id="quickSelectCards" class="quick-select-cards">
                                <!-- 动态生成卡片 -->
                            </div>
                        </div>
                    </div>

                    <form id="outboundForm">
                        <div class="form-group">
                            <label for="selectedProduct">已选商品:</label>
                            <div id="selectedProductInfo" class="selected-product-info">
                                <span class="no-selection">请先选择商品</span>
                            </div>
                            <input type="hidden" id="selectedProductCode" required>
                        </div>
                        <div class="form-group">
                            <label for="outQuantity">出库数量:</label>
                            <input type="number" id="outQuantity" min="1" required>
                        </div>
                        <div class="form-group">
                            <label for="outReason">出库原因:</label>
                            <select id="outReason" required>
                                <option value="">请选择出库原因</option>
                                <option value="销售">销售</option>
                                <option value="调拨">调拨</option>
                                <option value="损耗">损耗</option>
                                <option value="退货">退货</option>
                                <option value="其他">其他</option>
                            </select>
                        </div>
                        <div class="form-group">
                            <label for="outRemark">备注:</label>
                            <textarea id="outRemark" rows="3"></textarea>
                        </div>
                        <div class="form-actions">
                            <button type="submit">确认出库</button>
                            <button type="reset">重置表单</button>
                        </div>
                    </form>
                </div>

                <!-- 批量出库界面 -->
                <div id="batchOutboundContainer" class="batch-outbound-container" style="display: none;">
                    <!-- 商品搜索和筛选区域 -->
                    <div class="product-search-section">
                        <div class="search-controls">
                            <div class="form-group">
                                <label for="batchProductSearch">搜索商品:</label>
                                <input type="text" id="batchProductSearch" placeholder="输入商品名称、编号或规格搜索..." autocomplete="off">
                                <div id="batchSearchResults" class="search-results"></div>
                            </div>
                            <div class="form-group">
                                <label for="batchCategoryFilter">商品分类:</label>
                                <select id="batchCategoryFilter">
                                    <option value="">全部商品</option>
                                </select>
                            </div>
                        </div>

                        <!-- 组合套餐选择 -->
                        <div class="combo-select-section">
                            <h4>🎁 组合套餐 <span class="combo-count">(可用: <span id="availableComboCount">0</span> 个)</span></h4>
                            <div id="comboPackageCards" class="combo-package-cards">
                                <!-- 动态生成套餐卡片 -->
                            </div>
                        </div>

                        <!-- 快速选择卡片（批量模式） -->
                        <div class="quick-select-section">
                            <h4>📦 商品选择 <span class="cart-count">(已选: <span id="cartItemCount">0</span> 种)</span></h4>
                            <div id="batchQuickSelectCards" class="quick-select-cards batch-mode">
                                <!-- 动态生成卡片 -->
                            </div>
                        </div>
                    </div>

                    <!-- 出库清单 -->
                    <div class="outbound-cart-section">
                        <div class="cart-header">
                            <h3>📋 出库清单</h3>
                            <div class="cart-actions">
                                <button class="cart-action-btn" onclick="clearCart()">🗑️ 清空</button>
                                <button class="cart-action-btn primary" onclick="showBatchConfirmModal()" id="batchSubmitBtn" disabled>
                                    📦 批量出库 (<span id="cartTotalItems">0</span> 种商品)
                                </button>
                            </div>
                        </div>
                        <div class="cart-content">
                            <div id="cartItems" class="cart-items">
                                <div class="empty-cart">
                                    <p>🛒 出库清单为空</p>
                                    <p>点击上方商品卡片的 "+" 按钮添加商品</p>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- 出入库明细页面 -->
            <div id="transactions" class="tab-content">
                <div class="section-header">
                    <h2>出入库明细</h2>
                    <div class="search-box">
                        <select id="transactionTypeFilter">
                            <option value="">全部类型</option>
                            <option value="inbound">入库</option>
                            <option value="outbound">出库</option>
                        </select>
                        <input type="text" id="transactionSearchInput" placeholder="搜索商品名称或编号...">
                        <button onclick="searchTransactions()">搜索</button>
                        <button onclick="exportTransactions()" class="export-btn">📊 导出记录</button>
                    </div>
                </div>
                <div class="table-container">
                    <table id="transactionTable">
                        <thead>
                            <tr>
                                <th>时间</th>
                                <th>类型</th>
                                <th>商品编号</th>
                                <th>商品名称</th>
                                <th>数量</th>
                                <th>单位</th>
                                <th>原因/备注</th>
                                <th>操作员</th>
                            </tr>
                        </thead>
                        <tbody id="transactionTableBody">
                            <!-- 动态生成内容 -->
                        </tbody>
                    </table>
                </div>
            </div>

            <!-- 开发者选项页面 -->
            <div id="developer" class="tab-content">
                <div class="section-header">
                    <h2>🔧 开发者选项</h2>
                    <div class="developer-warning">
                        ⚠️ 注意：这些操作可能会影响系统数据，请谨慎使用！
                    </div>
                </div>

                <div class="developer-container">
                    <div class="developer-section">
                        <h3>📊 数据管理</h3>
                        <div class="developer-actions">
                            <button class="developer-action-btn backup-btn" onclick="createBackup()">
                                💾 备份当前数据
                            </button>
                            <button class="developer-action-btn restore-btn" onclick="showRestoreModal()">
                                📥 还原备份数据
                            </button>
                            <button class="developer-action-btn export-btn" onclick="exportAllData()">
                                📤 导出所有数据
                            </button>
                        </div>
                    </div>

                    <div class="developer-section">
                        <h3>🎁 套餐管理</h3>
                        <div class="developer-actions">
                            <button class="developer-action-btn combo-btn" onclick="showComboManageModal()">
                                🛠️ 管理套餐
                            </button>
                            <button class="developer-action-btn combo-btn" onclick="showCreateComboModal()">
                                ➕ 创建套餐
                            </button>
                        </div>
                        <div class="combo-summary">
                            <span>当前套餐数量：<strong id="devComboCount">0</strong></span>
                        </div>
                    </div>

                    <div class="developer-section">
                        <h3>🔄 系统重置</h3>
                        <div class="developer-actions">
                            <button class="developer-action-btn init-btn" onclick="showInitializeModal()">
                                🚀 初始化系统
                            </button>
                            <button class="developer-action-btn clear-btn" onclick="showClearDataModal()">
                                🗑️ 清空所有数据
                            </button>
                        </div>
                    </div>

                    <div class="developer-section">
                        <h3>📈 系统信息</h3>
                        <div class="system-info">
                            <div class="info-item">
                                <span class="info-label">库存商品数量：</span>
                                <span id="devInventoryCount">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">交易记录数量：</span>
                                <span id="devTransactionCount">0</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">数据存储大小：</span>
                                <span id="devStorageSize">0 KB</span>
                            </div>
                            <div class="info-item">
                                <span class="info-label">最后备份时间：</span>
                                <span id="devLastBackup">从未备份</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <!-- 消息提示 -->
    <div id="messageBox" class="message-box"></div>

    <!-- 确认对话框 -->
    <div id="confirmModal" class="modal">
        <div class="modal-content">
            <h3>操作确认</h3>
            <p id="confirmMessage"></p>
            <div class="modal-actions">
                <button id="confirmYes" class="btn-primary">确认</button>
                <button id="confirmNo" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 编辑对话框 -->
    <div id="editModal" class="modal">
        <div class="modal-content large">
            <h3>编辑商品信息</h3>
            <form id="editForm">
                <div class="edit-form-grid">
                    <div class="form-group">
                        <label for="editProductName">商品名称:</label>
                        <input type="text" id="editProductName" required>
                    </div>
                    <div class="form-group">
                        <label for="editProductCode">商品编号:</label>
                        <input type="text" id="editProductCode" readonly>
                        <small class="form-hint">商品编号不可修改</small>
                    </div>
                    <div class="form-group">
                        <label for="editSpecification">规格:</label>
                        <input type="text" id="editSpecification" placeholder="如：500ml、大号、红色等">
                    </div>
                    <div class="form-group">
                        <label for="editUnit">单位:</label>
                        <input type="text" id="editUnit" required>
                    </div>
                    <div class="form-group">
                        <label for="editQuantity">库存数量:</label>
                        <input type="number" id="editQuantity" min="0" required>
                    </div>
                    <div class="form-group">
                        <label for="editMinStock">最低库存:</label>
                        <input type="number" id="editMinStock" min="0" required>
                    </div>
                </div>
                <div class="form-group">
                    <label for="editRemark">备注:</label>
                    <textarea id="editRemark" rows="3" placeholder="商品描述或其他备注信息"></textarea>
                </div>
                <div class="edit-warning">
                    <h4>⚠️ 修改提醒</h4>
                    <ul>
                        <li>修改商品信息会自动记录到交易历史中</li>
                        <li>如果商品在组合套餐中，修改后需要检查套餐状态</li>
                        <li>库存数量的修改会影响可用库存计算</li>
                    </ul>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn-primary">💾 保存修改</button>
                    <button type="button" onclick="showDeleteConfirm()" class="btn-danger">🗑️ 删除商品</button>
                    <button type="button" onclick="closeEditModal()" class="btn-secondary">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 初始化确认对话框 -->
    <div id="initializeModal" class="modal">
        <div class="modal-content">
            <h3>🚀 系统初始化</h3>
            <p>初始化将清空所有现有数据并添加示例数据。</p>
            <p><strong>是否要在初始化前备份当前数据？</strong></p>
            <div class="modal-actions">
                <button onclick="initializeWithBackup()" class="btn-primary">备份后初始化</button>
                <button onclick="initializeWithoutBackup()" class="btn-warning">直接初始化</button>
                <button onclick="closeInitializeModal()" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 清空数据确认对话框 -->
    <div id="clearDataModal" class="modal">
        <div class="modal-content">
            <h3>🗑️ 清空所有数据</h3>
            <p>此操作将永久删除所有库存和交易记录！</p>
            <p><strong>是否要在清空前备份当前数据？</strong></p>
            <div class="modal-actions">
                <button onclick="clearDataWithBackup()" class="btn-primary">备份后清空</button>
                <button onclick="clearDataWithoutBackup()" class="btn-danger">直接清空</button>
                <button onclick="closeClearDataModal()" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 还原数据对话框 -->
    <div id="restoreModal" class="modal">
        <div class="modal-content">
            <h3>📥 还原备份数据</h3>
            <p>选择备份文件来还原数据：</p>
            <div class="form-group">
                <input type="file" id="restoreFileInput" accept=".json" style="margin-bottom: 15px;">
            </div>
            <div class="modal-actions">
                <button onclick="restoreFromFile()" class="btn-primary">还原数据</button>
                <button onclick="closeRestoreModal()" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 批量出库确认对话框 -->
    <div id="batchConfirmModal" class="modal">
        <div class="modal-content large">
            <h3>📦 批量出库确认</h3>
            <div class="batch-confirm-content">
                <div class="confirm-summary">
                    <div class="summary-item">
                        <span class="summary-label">出库商品种类：</span>
                        <span id="confirmTotalTypes">0</span> 种
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">出库原因：</span>
                        <select id="batchOutReason" required>
                            <option value="">请选择出库原因</option>
                            <option value="销售">销售</option>
                            <option value="调拨">调拨</option>
                            <option value="损耗">损耗</option>
                            <option value="退货">退货</option>
                            <option value="其他">其他</option>
                        </select>
                    </div>
                    <div class="summary-item">
                        <span class="summary-label">备注：</span>
                        <input type="text" id="batchOutRemark" placeholder="可选">
                    </div>
                </div>
                <div class="confirm-items">
                    <h4>出库明细：</h4>
                    <div id="confirmItemsList" class="confirm-items-list">
                        <!-- 动态生成 -->
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="executeBatchOutbound()" class="btn-primary">确认出库</button>
                <button onclick="closeBatchConfirmModal()" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 套餐管理对话框 -->
    <div id="comboManageModal" class="modal">
        <div class="modal-content large">
            <h3>🎁 套餐管理</h3>
            <div class="combo-manage-content">
                <div id="comboList" class="combo-list">
                    <!-- 动态生成套餐列表 -->
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="showCreateComboModal()" class="btn-primary">➕ 新建套餐</button>
                <button onclick="closeComboManageModal()" class="btn-secondary">关闭</button>
            </div>
        </div>
    </div>

    <!-- 创建/编辑套餐对话框 -->
    <div id="createComboModal" class="modal">
        <div class="modal-content large">
            <h3 id="comboModalTitle">🎁 创建套餐</h3>
            <form id="comboForm">
                <div class="form-group">
                    <label for="comboName">套餐名称:</label>
                    <input type="text" id="comboName" required>
                </div>
                <div class="form-group">
                    <label for="comboDescription">套餐描述:</label>
                    <input type="text" id="comboDescription" placeholder="可选">
                </div>
                <div class="form-group">
                    <label>包含商品:</label>
                    <div class="combo-items-section">
                        <div class="add-item-controls">
                            <select id="comboItemSelect">
                                <option value="">选择商品</option>
                            </select>
                            <input type="number" id="comboItemQuantity" min="1" value="1" placeholder="数量">
                            <button type="button" onclick="addComboItem()" class="btn-small">添加</button>
                        </div>
                        <div id="comboItemsList" class="combo-items-list">
                            <!-- 动态生成商品列表 -->
                        </div>
                    </div>
                </div>
                <div class="modal-actions">
                    <button type="submit" class="btn-primary">保存套餐</button>
                    <button type="button" onclick="closeCreateComboModal()" class="btn-secondary">取消</button>
                </div>
            </form>
        </div>
    </div>

    <!-- 导出选项配置对话框 -->
    <div id="exportModal" class="modal">
        <div class="modal-content large">
            <h3>📊 导出库存数据</h3>
            <div class="export-config">
                <div class="config-section">
                    <h4>导出格式</h4>
                    <div class="format-options">
                        <label class="format-option">
                            <input type="radio" name="exportFormat" value="excel" checked>
                            <span class="format-icon">📗</span>
                            <span class="format-name">Excel (.xlsx)</span>
                            <span class="format-desc">完整表格格式，支持公式和样式</span>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="exportFormat" value="image">
                            <span class="format-icon">🖼️</span>
                            <span class="format-name">图片 (.png)</span>
                            <span class="format-desc">高清图片格式，适合展示和打印</span>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="exportFormat" value="pdf">
                            <span class="format-icon">📄</span>
                            <span class="format-name">PDF (.pdf)</span>
                            <span class="format-desc">专业报表格式，包含统计信息</span>
                        </label>
                        <label class="format-option">
                            <input type="radio" name="exportFormat" value="csv">
                            <span class="format-icon">📋</span>
                            <span class="format-name">CSV (.csv)</span>
                            <span class="format-desc">纯数据格式，兼容性最好</span>
                        </label>
                    </div>
                </div>

                <div class="config-section">
                    <h4>导出范围</h4>
                    <div class="export-scope">
                        <label class="scope-option">
                            <input type="radio" name="exportScope" value="filtered" id="exportFiltered" checked>
                            <span>导出当前筛选结果</span>
                            <small id="filteredCount" class="scope-hint">当前显示: 0 项</small>
                        </label>
                        <label class="scope-option">
                            <input type="radio" name="exportScope" value="all" id="exportAll">
                            <span>导出全部库存数据</span>
                            <small id="totalCount" class="scope-hint">全部库存: 0 项</small>
                        </label>
                    </div>
                </div>

                <div class="config-section">
                    <h4>导出选项</h4>
                    <div class="export-options">
                        <label class="option-item">
                            <input type="checkbox" id="includeStats" checked>
                            <span>包含统计信息</span>
                        </label>
                        <label class="option-item">
                            <input type="checkbox" id="includeTimestamp" checked>
                            <span>包含导出时间</span>
                        </label>
                        <label class="option-item">
                            <input type="checkbox" id="onlyAvailable">
                            <span>仅导出有库存商品</span>
                        </label>
                        <label class="option-item">
                            <input type="checkbox" id="includeLowStock">
                            <span>标记库存不足商品</span>
                        </label>
                    </div>
                </div>

                <div class="config-section">
                    <h4>排序方式</h4>
                    <select id="exportSortBy">
                        <option value="name">按商品名称</option>
                        <option value="code">按商品编号</option>
                        <option value="quantity">按库存数量</option>
                        <option value="status">按库存状态</option>
                        <option value="lastUpdated">按更新时间</option>
                    </select>
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="executeExport()" class="btn-primary">开始导出</button>
                <button onclick="closeExportModal()" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 批量修改最低库存对话框 -->
    <div id="batchEditModal" class="modal">
        <div class="modal-content large">
            <h3>📝 批量修改最低库存</h3>
            <div class="batch-edit-content">
                <div class="selected-items-summary">
                    <h4>已选择商品：</h4>
                    <div id="selectedItemsList" class="selected-items-list">
                        <!-- 动态生成 -->
                    </div>
                </div>

                <div class="batch-edit-options">
                    <h4>修改方式：</h4>
                    <div class="edit-mode-selection">
                        <label class="edit-mode-option">
                            <input type="radio" name="editMode" value="setValue" checked>
                            <span>设置为固定值</span>
                        </label>
                        <label class="edit-mode-option">
                            <input type="radio" name="editMode" value="addValue">
                            <span>增加数值</span>
                        </label>
                        <label class="edit-mode-option">
                            <input type="radio" name="editMode" value="multiplyValue">
                            <span>按比例调整</span>
                        </label>
                    </div>

                    <div class="value-input-section">
                        <div class="form-group">
                            <label for="batchEditValue">数值：</label>
                            <input type="number" id="batchEditValue" min="0" value="10" required>
                            <span id="valueHint" class="value-hint">设置最低库存为此值</span>
                        </div>

                        <div class="quick-values">
                            <span>快速设置：</span>
                            <button type="button" class="quick-value-btn" onclick="setBatchValue(5)">5</button>
                            <button type="button" class="quick-value-btn" onclick="setBatchValue(10)">10</button>
                            <button type="button" class="quick-value-btn" onclick="setBatchValue(20)">20</button>
                            <button type="button" class="quick-value-btn" onclick="setBatchValue(50)">50</button>
                            <button type="button" class="quick-value-btn" onclick="setBatchValue(100)">100</button>
                        </div>
                    </div>
                </div>

                <div class="preview-section">
                    <h4>预览修改结果：</h4>
                    <div id="previewResults" class="preview-results">
                        <!-- 动态生成预览 -->
                    </div>
                </div>
            </div>
            <div class="modal-actions">
                <button onclick="executeBatchEdit()" class="btn-primary">确认修改</button>
                <button onclick="closeBatchEditModal()" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <!-- 删除商品确认对话框 -->
    <div id="deleteConfirmModal" class="modal">
        <div class="modal-content">
            <h3>🗑️ 删除商品确认</h3>
            <div class="delete-warning">
                <div class="warning-icon">⚠️</div>
                <div class="warning-content">
                    <h4>您即将删除以下商品：</h4>
                    <div id="deleteProductInfo" class="delete-product-info">
                        <!-- 动态填充商品信息 -->
                    </div>
                    <div class="warning-details">
                        <h5>删除后的影响：</h5>
                        <ul id="deleteImpactList">
                            <!-- 动态填充影响列表 -->
                        </ul>
                        <div class="final-warning">
                            <strong>⚠️ 此操作不可撤销！请谨慎操作！</strong>
                        </div>
                    </div>
                </div>
            </div>
            <div class="delete-confirmation">
                <label class="confirmation-checkbox">
                    <input type="checkbox" id="deleteConfirmCheckbox">
                    <span>我已了解删除的影响，确认要删除此商品</span>
                </label>
            </div>
            <div class="modal-actions">
                <button onclick="executeDelete()" class="btn-danger" id="confirmDeleteBtn" disabled>确认删除</button>
                <button onclick="closeDeleteConfirmModal()" class="btn-secondary">取消</button>
            </div>
        </div>
    </div>

    <script src="script.js"></script>
</body>
</html>
