* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Microsoft YaHei', Arial, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1400px;
    margin: 0 auto;
    background-color: white;
    min-height: 100vh;
    box-shadow: 0 0 10px rgba(0,0,0,0.1);
    padding: 0 20px;
}

.header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 20px;
    text-align: center;
}

.header h1 {
    margin-bottom: 20px;
    font-size: 2.5em;
    font-weight: 300;
}

.nav {
    display: flex;
    justify-content: center;
    gap: 10px;
}

.nav-btn {
    background: rgba(255,255,255,0.2);
    color: white;
    border: 2px solid rgba(255,255,255,0.3);
    padding: 12px 24px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 16px;
}

.nav-btn:hover {
    background: rgba(255,255,255,0.3);
    transform: translateY(-2px);
}

.nav-btn.active {
    background: white;
    color: #667eea;
    border-color: white;
}

.main {
    padding: 30px;
}

.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

.section-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 30px;
    padding-bottom: 15px;
    border-bottom: 2px solid #eee;
}

.section-header h2 {
    color: #667eea;
    font-size: 1.8em;
}

.search-box {
    display: flex;
    gap: 10px;
    align-items: center;
    flex-wrap: wrap;
    background: #f8f9fa;
    padding: 15px;
    border-radius: 10px;
    margin-bottom: 10px;
}

.search-box input {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 25px;
    width: 350px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.search-box input:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box select {
    padding: 10px 15px;
    border: 2px solid #ddd;
    border-radius: 25px;
    font-size: 14px;
    background: white;
    min-width: 150px;
    transition: border-color 0.3s ease;
}

.search-box select:focus {
    outline: none;
    border-color: #667eea;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
}

.search-box button {
    background: #667eea;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: background 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.search-box button:hover {
    background: #5a6fd8;
}

.header-controls {
    display: flex;
    gap: 20px;
    align-items: center;
    flex-wrap: wrap;
}

.export-controls {
    position: relative;
    display: flex;
    gap: 10px;
    align-items: center;
}

.export-main-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 10px 20px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
    font-weight: 500;
}

.export-main-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

.export-dropdown {
    display: flex;
    gap: 5px;
}

.export-btn {
    background: white;
    border: 2px solid #ddd;
    padding: 8px 12px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 12px;
    color: #666;
}

.export-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 10px rgba(0,0,0,0.1);
}

.excel-btn:hover {
    border-color: #28a745;
    color: #28a745;
}

.image-btn:hover {
    border-color: #17a2b8;
    color: #17a2b8;
}

.pdf-btn:hover {
    border-color: #dc3545;
    color: #dc3545;
}

.csv-btn:hover {
    border-color: #ffc107;
    color: #856404;
}

.edit-btn {
    background: #28a745 !important;
    margin-left: 10px;
}

.edit-btn:hover {
    background: #218838 !important;
}

.edit-btn.unlocked {
    background: #ffc107 !important;
    color: #333 !important;
}

.edit-btn.unlocked:hover {
    background: #e0a800 !important;
}

.clear-btn {
    background: #6c757d !important;
    margin-left: 5px;
}

.clear-btn:hover {
    background: #545b62 !important;
}

.batch-edit-btn {
    background: #fd7e14 !important;
    margin-left: 5px;
}

.batch-edit-btn:hover {
    background: #e8650e !important;
}

.clear-selection-btn {
    background: #dc3545 !important;
    margin-left: 5px;
}

.clear-selection-btn:hover {
    background: #c82333 !important;
}

.export-btn {
    background: #17a2b8 !important;
    margin-left: 10px;
}

.export-btn:hover {
    background: #138496 !important;
}

.inventory-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 25px;
    margin-bottom: 30px;
    max-width: 1000px;
    margin-left: auto;
    margin-right: auto;
}

.stat-card {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    color: white;
    padding: 25px;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.stat-card h3 {
    font-size: 14px;
    margin-bottom: 10px;
    opacity: 0.9;
}

.stat-card span {
    font-size: 2.5em;
    font-weight: bold;
}

.table-container {
    background: white;
    border-radius: 10px;
    overflow: hidden;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    max-height: 70vh;
    overflow-y: auto;
}

table {
    width: 100%;
    border-collapse: collapse;
}

th {
    background: #667eea;
    color: white;
    padding: 15px;
    text-align: left;
    font-weight: 500;
    position: sticky;
    top: 0;
    z-index: 10;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    /* 禁用表头的悬停效果 */
    transition: none !important;
    transform: none !important;
}

/* 确保表头行不受悬停效果影响 */
thead tr:hover {
    background: #667eea !important;
    transform: none !important;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1) !important;
}

td {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    vertical-align: middle;
}

/* 商品分组样式 */
.product-group-start {
    border-top: 3px solid #667eea;
}

.product-group-member {
    background: rgba(102, 126, 234, 0.03);
}

.product-group-member:nth-child(even) {
    background: rgba(102, 126, 234, 0.06);
}

/* 斑马纹效果 */
tr:nth-child(even):not(.product-group-member) {
    background: #f8f9fa;
}

/* 只对表格主体的行应用悬停效果，不包括表头 */
tbody tr:hover {
    background-color: #e3f2fd !important;
    transform: scale(1.005);
    transition: all 0.2s ease;
    box-shadow: 0 2px 8px rgba(0,0,0,0.1);
}

/* 商品名称分组指示器 */
.group-indicator {
    font-size: 11px;
    color: #6c757d;
    font-weight: normal;
    background: rgba(108, 117, 125, 0.1);
    padding: 2px 6px;
    border-radius: 8px;
    margin-left: 8px;
}

/* 状态样式优化 */
.status-normal {
    color: #28a745;
    font-weight: bold;
    background: rgba(40, 167, 69, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: inline-block;
}

.status-low {
    color: #ffc107;
    font-weight: bold;
    background: rgba(255, 193, 7, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: inline-block;
}

.status-out {
    color: #dc3545;
    font-weight: bold;
    background: rgba(220, 53, 69, 0.1);
    padding: 4px 8px;
    border-radius: 12px;
    font-size: 12px;
    display: inline-block;
}

.form-container {
    max-width: 800px;
    margin: 0 auto;
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 8px;
    font-weight: 500;
    color: #555;
}

.form-group input,
.form-group select,
.form-group textarea {
    width: 100%;
    padding: 12px 15px;
    border: 2px solid #ddd;
    border-radius: 8px;
    font-size: 14px;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #667eea;
}

.form-hint {
    display: block;
    margin-top: 5px;
    font-size: 12px;
    color: #6c757d;
    font-style: italic;
}

.form-group input[readonly] {
    background-color: #f8f9fa;
    color: #6c757d;
}

.form-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 30px;
}

.form-actions button {
    padding: 12px 30px;
    border: none;
    border-radius: 25px;
    font-size: 16px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.form-actions button[type="submit"] {
    background: #667eea;
    color: white;
}

.form-actions button[type="submit"]:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.form-actions button[type="reset"] {
    background: #6c757d;
    color: white;
}

.form-actions button[type="reset"]:hover {
    background: #5a6268;
}

.message-box {
    position: fixed;
    top: 20px;
    right: 20px;
    padding: 15px 25px;
    border-radius: 8px;
    color: white;
    font-weight: 500;
    z-index: 1000;
    transform: translateX(400px);
    transition: transform 0.3s ease;
}

.message-box.show {
    transform: translateX(0);
}

.message-box.success {
    background: #28a745;
}

.message-box.error {
    background: #dc3545;
}

.message-box.warning {
    background: #ffc107;
    color: #333;
}

/* 模态框样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 2000;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0,0,0,0.5);
    animation: fadeIn 0.3s ease;
}

.modal.show {
    display: flex;
    align-items: center;
    justify-content: center;
}

.modal-content {
    background: white;
    padding: 30px;
    border-radius: 15px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    max-width: 500px;
    width: 90%;
    animation: slideIn 0.3s ease;
}

.modal-content h3 {
    margin-bottom: 20px;
    color: #667eea;
    text-align: center;
}

.modal-actions {
    display: flex;
    gap: 15px;
    justify-content: center;
    margin-top: 25px;
}

.btn-primary {
    background: #667eea;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-primary:hover {
    background: #5a6fd8;
    transform: translateY(-2px);
}

.btn-secondary {
    background: #6c757d;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-secondary:hover {
    background: #5a6268;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.btn-danger:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.edit-action-btn {
    background: #28a745;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    margin: 0 2px;
    transition: background 0.3s ease;
}

.edit-action-btn:hover {
    background: #218838;
}

.edit-action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
}

.detail-action-btn {
    background: #007bff;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    margin: 0 2px;
    transition: background 0.3s ease;
}

.detail-action-btn:hover {
    background: #0056b3;
}

/* 合并商品详细信息样式 */
.merged-item-details {
    max-width: 800px;
    text-align: left;
}

.detail-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 15px;
    border-left: 4px solid #007bff;
}

.detail-summary p {
    margin: 5px 0;
    font-size: 14px;
}

.detail-items h5 {
    margin: 15px 0 10px 0;
    color: #333;
}

.detail-table {
    width: 100%;
    border-collapse: collapse;
    font-size: 12px;
}

.detail-table th,
.detail-table td {
    border: 1px solid #ddd;
    padding: 8px;
    text-align: left;
}

.detail-table th {
    background: #f8f9fa;
    font-weight: bold;
}

.detail-table tr:nth-child(even) {
    background: #f9f9f9;
}

/* 批量编辑样式 */
.batch-edit-content {
    max-height: 500px;
    overflow-y: auto;
}

.selected-items-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
    border-left: 4px solid #fd7e14;
}

.selected-items-list {
    max-height: 120px;
    overflow-y: auto;
    margin-top: 10px;
}

.selected-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 12px;
    background: white;
    margin-bottom: 5px;
    border-radius: 6px;
    border: 1px solid #e9ecef;
}

.batch-edit-options {
    margin-bottom: 20px;
}

.edit-mode-selection {
    display: flex;
    gap: 20px;
    margin: 15px 0;
    flex-wrap: wrap;
}

.edit-mode-option {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    transition: all 0.3s ease;
}

.edit-mode-option:hover {
    border-color: #fd7e14;
    background: rgba(253, 126, 20, 0.1);
}

.edit-mode-option input[type="radio"]:checked + span {
    color: #fd7e14;
    font-weight: bold;
}

.value-input-section {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin: 15px 0;
}

.value-hint {
    font-size: 12px;
    color: #6c757d;
    margin-left: 10px;
}

.quick-values {
    margin-top: 15px;
    display: flex;
    align-items: center;
    gap: 10px;
    flex-wrap: wrap;
}

.quick-value-btn {
    background: #fd7e14;
    color: white;
    border: none;
    padding: 6px 12px;
    border-radius: 15px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.quick-value-btn:hover {
    background: #e8650e;
}

.preview-section {
    background: #e3f2fd;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #2196f3;
}

.preview-results {
    max-height: 150px;
    overflow-y: auto;
    margin-top: 10px;
}

.preview-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 6px 10px;
    background: white;
    margin-bottom: 3px;
    border-radius: 4px;
    font-size: 13px;
}

.preview-change {
    color: #fd7e14;
    font-weight: bold;
}

/* 表格复选框样式 */
.item-checkbox {
    transform: scale(1.2);
    margin-right: 8px;
}

.selected-row {
    background: rgba(253, 126, 20, 0.1) !important;
    border-left: 3px solid #fd7e14;
}

/* 信息类型消息框样式 */
.message-box.info {
    background: #17a2b8;
    color: white;
    max-width: 900px;
    width: auto;
}

/* 文本颜色辅助类 */
.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

/* 表格响应式优化 */
@media (max-width: 1024px) {
    .container {
        max-width: 95%;
        padding: 0 15px;
    }
}

@media (max-width: 768px) {
    .table-container {
        font-size: 12px;
    }

    th, td {
        padding: 8px 6px;
    }

    .search-box {
        flex-direction: column;
        align-items: stretch;
    }

    .search-box input,
    .search-box select {
        width: 100%;
        margin-bottom: 10px;
    }

    .container {
        padding: 0 10px;
    }
}

.transaction-inbound {
    color: #28a745;
    font-weight: bold;
}

.transaction-outbound {
    color: #dc3545;
    font-weight: bold;
}

/* 商品分组样式 */
.product-group-start {
    border-top: 3px solid #667eea !important;
}

.product-group-member {
    background-color: #f8f9ff !important;
}

.product-group-member:hover {
    background-color: #e8ebff !important;
}

.group-indicator {
    font-size: 12px;
    color: #667eea;
    font-weight: bold;
    background: rgba(102, 126, 234, 0.1);
    padding: 2px 6px;
    border-radius: 10px;
    margin-left: 8px;
}

/* 开发者选项样式 */
.developer-btn {
    background: #dc3545 !important;
    border-color: #dc3545 !important;
}

.developer-btn:hover {
    background: #c82333 !important;
}

.developer-warning {
    background: #fff3cd;
    color: #856404;
    padding: 10px 15px;
    border-radius: 8px;
    border: 1px solid #ffeaa7;
    font-size: 14px;
    text-align: center;
}

.developer-container {
    max-width: 1000px;
    margin: 0 auto;
}

.developer-section {
    background: white;
    margin-bottom: 25px;
    padding: 25px;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
}

.developer-section h3 {
    color: #667eea;
    margin-bottom: 20px;
    font-size: 1.3em;
    border-bottom: 2px solid #eee;
    padding-bottom: 10px;
}

.developer-actions {
    display: flex;
    gap: 15px;
    flex-wrap: wrap;
}

.developer-action-btn {
    padding: 12px 20px;
    border: none;
    border-radius: 8px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.3s ease;
    min-width: 150px;
}

.backup-btn {
    background: #28a745;
    color: white;
}

.backup-btn:hover {
    background: #218838;
    transform: translateY(-2px);
}

.restore-btn {
    background: #17a2b8;
    color: white;
}

.restore-btn:hover {
    background: #138496;
    transform: translateY(-2px);
}

.export-btn {
    background: #6f42c1;
    color: white;
}

.export-btn:hover {
    background: #5a32a3;
    transform: translateY(-2px);
}

.init-btn {
    background: #ffc107;
    color: #333;
}

.init-btn:hover {
    background: #e0a800;
    transform: translateY(-2px);
}

.clear-btn {
    background: #dc3545;
    color: white;
}

.clear-btn:hover {
    background: #c82333;
    transform: translateY(-2px);
}

.system-info {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 10px;
    border: 1px solid #dee2e6;
}

.info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    padding: 8px 0;
    border-bottom: 1px solid #e9ecef;
}

.info-item:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.info-label {
    font-weight: 500;
    color: #495057;
}

.btn-warning {
    background: #ffc107;
    color: #333;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-warning:hover {
    background: #e0a800;
}

.btn-danger {
    background: #dc3545;
    color: white;
    border: none;
    padding: 12px 25px;
    border-radius: 25px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.btn-danger:hover {
    background: #c82333;
}

#systemTitle {
    cursor: pointer;
    user-select: none;
}

#systemTitle:hover {
    opacity: 0.8;
}

/* 出库商品选择优化样式 */
.product-search-section {
    background: #f8f9ff;
    padding: 20px;
    border-radius: 15px;
    margin-bottom: 25px;
    border: 2px solid #e8ebff;
}

.search-controls {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 20px;
    margin-bottom: 20px;
}

.search-results {
    position: relative;
    max-height: 200px;
    overflow-y: auto;
    background: white;
    border: 1px solid #ddd;
    border-radius: 8px;
    margin-top: 5px;
    display: none;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    z-index: 100;
}

.search-results.show {
    display: block;
}

.search-result-item {
    padding: 12px 15px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background 0.2s ease;
}

.search-result-item:hover {
    background: #f8f9ff;
}

.search-result-item:last-child {
    border-bottom: none;
}

.result-name {
    font-weight: 500;
    color: #333;
}

.result-details {
    font-size: 12px;
    color: #666;
    margin-top: 3px;
}

.result-stock {
    font-size: 12px;
    font-weight: bold;
    margin-top: 3px;
}

.stock-normal {
    color: #28a745;
}

.stock-low {
    color: #ffc107;
}

.stock-out {
    color: #dc3545;
}

.quick-select-section h4 {
    color: #667eea;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.quick-select-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
}

.quick-select-card {
    background: white;
    border: 2px solid #e8ebff;
    border-radius: 10px;
    padding: 15px;
    cursor: pointer;
    transition: all 0.3s ease;
    text-align: center;
}

.quick-select-card:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.2);
}

.quick-select-card.selected {
    border-color: #667eea;
    background: #f8f9ff;
}

.card-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.card-details {
    font-size: 12px;
    color: #666;
    margin-bottom: 8px;
}

.card-stock {
    font-size: 14px;
    font-weight: bold;
}

.selected-product-info {
    background: #f8f9fa;
    border: 2px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    min-height: 60px;
    display: flex;
    align-items: center;
}

.selected-product-info.has-selection {
    background: #e8f5e8;
    border-color: #28a745;
}

.no-selection {
    color: #6c757d;
    font-style: italic;
}

.selected-product-details {
    width: 100%;
}

.selected-name {
    font-weight: bold;
    color: #333;
    font-size: 16px;
}

.selected-info {
    color: #666;
    font-size: 14px;
    margin-top: 5px;
}

.selected-stock {
    font-weight: bold;
    margin-top: 5px;
}

@media (max-width: 768px) {
    .search-controls {
        grid-template-columns: 1fr;
    }

    .quick-select-cards {
        grid-template-columns: 1fr;
    }
}

/* 批量出库样式 */
.outbound-mode-switch {
    display: flex;
    gap: 10px;
}

.mode-btn {
    background: rgba(255,255,255,0.2);
    color: #667eea;
    border: 2px solid #667eea;
    padding: 8px 20px;
    border-radius: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-size: 14px;
}

.mode-btn:hover {
    background: rgba(102, 126, 234, 0.1);
}

.mode-btn.active {
    background: #667eea;
    color: white;
}

.batch-outbound-container {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 25px;
    margin-top: 20px;
}

.quick-select-cards.batch-mode .quick-select-card {
    position: relative;
}

.add-to-cart-btn {
    position: absolute;
    top: 10px;
    right: 10px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 50%;
    width: 30px;
    height: 30px;
    cursor: pointer;
    font-size: 16px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
    box-shadow: 0 2px 5px rgba(0,0,0,0.2);
}

.add-to-cart-btn:hover {
    background: #218838;
    transform: scale(1.1);
}

.add-to-cart-btn:disabled {
    background: #6c757d;
    cursor: not-allowed;
    transform: none;
}

.cart-count {
    color: #667eea;
    font-weight: bold;
    font-size: 14px;
}

.outbound-cart-section {
    background: white;
    border-radius: 15px;
    box-shadow: 0 5px 15px rgba(0,0,0,0.1);
    height: fit-content;
    max-height: 600px;
    display: flex;
    flex-direction: column;
}

.cart-header {
    padding: 20px;
    border-bottom: 2px solid #eee;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.cart-header h3 {
    color: #667eea;
    margin: 0;
}

.cart-actions {
    display: flex;
    gap: 10px;
}

.cart-action-btn {
    padding: 8px 15px;
    border: none;
    border-radius: 20px;
    cursor: pointer;
    font-size: 12px;
    transition: all 0.3s ease;
}

.cart-action-btn:not(.primary) {
    background: #6c757d;
    color: white;
}

.cart-action-btn.primary {
    background: #667eea;
    color: white;
}

.cart-action-btn:hover {
    transform: translateY(-1px);
}

.cart-action-btn:disabled {
    background: #ccc;
    cursor: not-allowed;
    transform: none;
}

.cart-content {
    flex: 1;
    overflow-y: auto;
    padding: 20px;
}

.empty-cart {
    text-align: center;
    color: #999;
    padding: 40px 20px;
}

.empty-cart p {
    margin: 10px 0;
}

.cart-items {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.cart-item {
    background: #f8f9ff;
    border: 1px solid #e8ebff;
    border-radius: 10px;
    padding: 15px;
    position: relative;
}

.cart-item-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 10px;
}

.cart-item-name {
    font-weight: bold;
    color: #333;
    font-size: 14px;
}

.cart-item-remove {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-item-details {
    font-size: 12px;
    color: #666;
    margin-bottom: 10px;
}

.cart-item-quantity {
    display: flex;
    align-items: center;
    gap: 10px;
}

.quantity-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.quantity-btn {
    background: #667eea;
    color: white;
    border: none;
    border-radius: 3px;
    width: 25px;
    height: 25px;
    cursor: pointer;
    font-size: 14px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.quantity-input {
    width: 60px;
    text-align: center;
    border: 1px solid #ddd;
    border-radius: 3px;
    padding: 3px;
}

.modal-content.large {
    max-width: 900px;
    width: 95%;
}

.batch-confirm-content {
    max-height: 400px;
    overflow-y: auto;
}

.confirm-summary {
    background: #f8f9fa;
    padding: 15px;
    border-radius: 8px;
    margin-bottom: 20px;
}

.summary-item {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.summary-item:last-child {
    margin-bottom: 0;
}

.summary-label {
    font-weight: 500;
    min-width: 120px;
    color: #495057;
}

.summary-item select,
.summary-item input {
    flex: 1;
    padding: 5px 10px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.confirm-items-list {
    background: #f8f9ff;
    border: 1px solid #e8ebff;
    border-radius: 8px;
    padding: 15px;
    max-height: 200px;
    overflow-y: auto;
}

.confirm-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-bottom: 1px solid #e8ebff;
}

.confirm-item:last-child {
    border-bottom: none;
}

.confirm-item-info {
    flex: 1;
}

.confirm-item-name {
    font-weight: bold;
    color: #333;
}

.confirm-item-details {
    font-size: 12px;
    color: #666;
}

.confirm-item-quantity {
    font-weight: bold;
    color: #667eea;
    min-width: 80px;
    text-align: right;
}

@media (max-width: 768px) {
    .batch-outbound-container {
        grid-template-columns: 1fr;
    }

    .outbound-mode-switch {
        justify-content: center;
        margin-top: 15px;
    }

    .cart-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }

    .cart-actions {
        justify-content: center;
    }
}

/* 组合套餐样式 */
.combo-select-section {
    margin-bottom: 25px;
}

.combo-select-section h4 {
    color: #e91e63;
    margin-bottom: 15px;
    font-size: 1.1em;
}

.combo-count {
    color: #e91e63;
    font-weight: bold;
    font-size: 14px;
}

.combo-package-cards {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 15px;
    margin-bottom: 20px;
}

.combo-package-card {
    background: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    border: 2px solid #f8bbd9;
    border-radius: 15px;
    padding: 20px;
    cursor: pointer;
    transition: all 0.3s ease;
    position: relative;
    color: white;
}

.combo-package-card:hover {
    transform: translateY(-3px);
    box-shadow: 0 8px 25px rgba(233, 30, 99, 0.3);
}

.combo-package-card.unavailable {
    background: linear-gradient(135deg, #bbb 0%, #ddd 100%);
    border-color: #ccc;
    cursor: not-allowed;
    opacity: 0.6;
}

.combo-package-card.low-stock {
    background: linear-gradient(135deg, #ffc107 0%, #ffeb3b 100%);
    border-color: #ffc107;
    color: #333;
}

.combo-card-header {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 15px;
}

.combo-card-name {
    font-weight: bold;
    font-size: 16px;
    margin-bottom: 5px;
}

.combo-card-description {
    font-size: 12px;
    opacity: 0.9;
}

.combo-card-status {
    background: rgba(255,255,255,0.2);
    padding: 4px 8px;
    border-radius: 10px;
    font-size: 11px;
    font-weight: bold;
}

.combo-card-items {
    background: rgba(255,255,255,0.1);
    border-radius: 8px;
    padding: 10px;
    margin-bottom: 10px;
}

.combo-card-items h5 {
    margin: 0 0 8px 0;
    font-size: 12px;
    opacity: 0.9;
}

.combo-item {
    font-size: 11px;
    margin-bottom: 3px;
    opacity: 0.8;
}

.combo-card-available {
    text-align: center;
    font-weight: bold;
    font-size: 14px;
}

.combo-btn {
    background: #e91e63 !important;
    color: white;
}

.combo-btn:hover {
    background: #c2185b !important;
}

.combo-summary {
    margin-top: 15px;
    padding: 10px;
    background: #f8f9fa;
    border-radius: 8px;
    text-align: center;
    font-size: 14px;
    color: #495057;
}

.combo-manage-content {
    max-height: 400px;
    overflow-y: auto;
}

.combo-list {
    display: flex;
    flex-direction: column;
    gap: 15px;
}

.combo-list-item {
    background: #f8f9ff;
    border: 1px solid #e8ebff;
    border-radius: 10px;
    padding: 15px;
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
}

.combo-info {
    flex: 1;
}

.combo-info-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 5px;
}

.combo-info-description {
    color: #666;
    font-size: 14px;
    margin-bottom: 8px;
}

.combo-info-items {
    font-size: 12px;
    color: #888;
}

.combo-actions {
    display: flex;
    gap: 8px;
}

.combo-action-btn {
    padding: 5px 10px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
    transition: background 0.3s ease;
}

.combo-action-btn.edit {
    background: #17a2b8;
    color: white;
}

.combo-action-btn.delete {
    background: #dc3545;
    color: white;
}

.combo-items-section {
    border: 1px solid #ddd;
    border-radius: 8px;
    padding: 15px;
    background: #f8f9fa;
}

.add-item-controls {
    display: flex;
    gap: 10px;
    margin-bottom: 15px;
    align-items: center;
}

.add-item-controls select,
.add-item-controls input {
    flex: 1;
    padding: 8px;
    border: 1px solid #ddd;
    border-radius: 5px;
}

.btn-small {
    padding: 8px 15px;
    background: #28a745;
    color: white;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 12px;
}

.combo-items-list {
    min-height: 100px;
    max-height: 200px;
    overflow-y: auto;
    border: 1px solid #ddd;
    border-radius: 5px;
    background: white;
    padding: 10px;
}

.combo-item-entry {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 8px;
    border-bottom: 1px solid #eee;
    background: #f8f9ff;
    border-radius: 5px;
    margin-bottom: 5px;
}

.combo-item-entry:last-child {
    margin-bottom: 0;
}

.combo-item-info {
    flex: 1;
}

.combo-item-name {
    font-weight: bold;
    color: #333;
}

.combo-item-details {
    font-size: 12px;
    color: #666;
}

.combo-item-remove {
    background: #dc3545;
    color: white;
    border: none;
    border-radius: 3px;
    width: 20px;
    height: 20px;
    cursor: pointer;
    font-size: 12px;
}

@media (max-width: 768px) {
    .combo-package-cards {
        grid-template-columns: 1fr;
    }

    .add-item-controls {
        flex-direction: column;
    }

    .combo-list-item {
        flex-direction: column;
        gap: 10px;
    }

    .combo-actions {
        align-self: flex-end;
    }
}

/* 导出配置样式 */
.export-config {
    max-height: 500px;
    overflow-y: auto;
    padding: 10px;
}

.config-section {
    margin-bottom: 25px;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 10px;
    border: 1px solid #e9ecef;
}

.config-section h4 {
    color: #495057;
    margin-bottom: 15px;
    font-size: 1.1em;
    border-bottom: 2px solid #dee2e6;
    padding-bottom: 8px;
}

/* 导出范围选择样式 */
.export-scope {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.scope-option {
    display: flex;
    flex-direction: column;
    padding: 15px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.scope-option:hover {
    border-color: #667eea;
    background: rgba(102, 126, 234, 0.05);
}

.scope-option input[type="radio"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.scope-option input[type="radio"]:checked + span {
    color: #667eea;
    font-weight: bold;
}

.scope-hint {
    font-size: 12px;
    color: #6c757d;
    margin-top: 5px;
    margin-left: 25px;
    font-style: italic;
}

.format-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 15px;
}

.format-option {
    display: flex;
    align-items: center;
    padding: 15px;
    background: white;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.format-option:hover {
    border-color: #667eea;
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.1);
}

.format-option input[type="radio"] {
    margin-right: 12px;
    transform: scale(1.2);
}

.format-option input[type="radio"]:checked + .format-icon {
    transform: scale(1.2);
}

.format-option:has(input:checked) {
    border-color: #667eea;
    background: #f8f9ff;
}

.format-icon {
    font-size: 24px;
    margin-right: 12px;
    transition: transform 0.3s ease;
}

.format-name {
    font-weight: bold;
    color: #333;
    margin-bottom: 3px;
    display: block;
}

.format-desc {
    font-size: 12px;
    color: #666;
    line-height: 1.3;
}

.export-options {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 12px;
}

.option-item {
    display: flex;
    align-items: center;
    padding: 10px;
    background: white;
    border: 1px solid #e9ecef;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.option-item:hover {
    background: #f8f9ff;
    border-color: #667eea;
}

.option-item input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.1);
}

.option-item span {
    font-size: 14px;
    color: #495057;
}

.config-section select {
    width: 100%;
    padding: 10px 15px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    background: white;
    font-size: 14px;
    color: #495057;
    transition: border-color 0.3s ease;
}

.config-section select:focus {
    outline: none;
    border-color: #667eea;
}

@media (max-width: 768px) {
    .header-controls {
        flex-direction: column;
        align-items: stretch;
        gap: 15px;
    }

    .export-controls {
        justify-content: center;
    }

    .export-dropdown {
        flex-wrap: wrap;
        justify-content: center;
    }

    .format-options {
        grid-template-columns: 1fr;
    }

    .export-options {
        grid-template-columns: 1fr;
    }
}

/* 编辑表单样式 */
.edit-form-grid {
    display: grid;
    grid-template-columns: repeat(2, 1fr);
    gap: 20px;
    margin-bottom: 20px;
}

.edit-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.edit-warning h4 {
    color: #856404;
    margin-bottom: 10px;
    font-size: 14px;
}

.edit-warning ul {
    margin: 0;
    padding-left: 20px;
    color: #856404;
    font-size: 12px;
}

.edit-warning li {
    margin-bottom: 5px;
}

/* 删除确认对话框样式 */
.delete-warning {
    display: flex;
    gap: 15px;
    margin-bottom: 20px;
    padding: 20px;
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    border-radius: 8px;
}

.warning-icon {
    font-size: 32px;
    color: #dc3545;
    flex-shrink: 0;
}

.warning-content {
    flex: 1;
}

.warning-content h4 {
    color: #721c24;
    margin-bottom: 10px;
    font-size: 16px;
}

.warning-content h5 {
    color: #721c24;
    margin: 15px 0 8px 0;
    font-size: 14px;
}

.delete-product-info {
    background: white;
    border: 1px solid #dc3545;
    border-radius: 5px;
    padding: 10px;
    margin-bottom: 15px;
}

.product-info-item {
    display: flex;
    justify-content: space-between;
    margin-bottom: 5px;
    font-size: 14px;
}

.product-info-label {
    font-weight: bold;
    color: #721c24;
}

.product-info-value {
    color: #495057;
}

.warning-details ul {
    margin: 0;
    padding-left: 20px;
    color: #721c24;
    font-size: 13px;
}

.warning-details li {
    margin-bottom: 5px;
}

.final-warning {
    background: #dc3545;
    color: white;
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    margin-top: 15px;
    font-size: 14px;
}

.delete-confirmation {
    background: #f8f9fa;
    border: 1px solid #dee2e6;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 20px;
}

.confirmation-checkbox {
    display: flex;
    align-items: center;
    cursor: pointer;
    font-size: 14px;
    color: #495057;
}

.confirmation-checkbox input[type="checkbox"] {
    margin-right: 10px;
    transform: scale(1.2);
}

.confirmation-checkbox:hover {
    color: #dc3545;
}

@media (max-width: 768px) {
    .edit-form-grid {
        grid-template-columns: 1fr;
    }

    .delete-warning {
        flex-direction: column;
        text-align: center;
    }

    .warning-icon {
        align-self: center;
    }
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideIn {
    from { transform: translateY(-50px); opacity: 0; }
    to { transform: translateY(0); opacity: 1; }
}

@media (max-width: 768px) {
    .nav {
        flex-direction: column;
        gap: 5px;
    }
    
    .section-header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .search-box {
        justify-content: center;
    }
    
    .search-box input {
        width: 250px;
    }
    
    .inventory-stats {
        grid-template-columns: 1fr;
    }
    
    .table-container {
        overflow-x: auto;
    }
    
    .form-actions {
        flex-direction: column;
    }
}
